import pandas as pd
import numpy as np
import os
import re
import sys
import math
import pickle
from datetime import datetime
from tabulate import tabulate
import warnings

# 导入资金流查询工具
try:
    import fund_flow_query_basic as fq
except ImportError:
    print("警告: 无法导入 fund_flow_query_basic，历史最大值功能将不可用")
    fq = None

# 移除akshare_data_fetcher依赖，改用数据库查询
# try:
#     import akshare_data_fetcher
# except ImportError:
#     print("警告: 无法导入 akshare_data_fetcher，将使用基础板块功能")
#     akshare_data_fetcher = None

# 导入数据库查询模块
import sqlite3

# 导入概念和板块过滤器
from concept_sector_filter import filter_meaningful_concepts_and_sectors, get_meaningless_items

# ——— 数据库配置 ———
# 股票板块数据库路径配置
STOCK_BLOCK_DB_PATH = r'D:\dev\mootdx\stock_block_analysis.db'
# 备用路径（当前目录）
STOCK_BLOCK_DB_FALLBACK = 'stock_block_analysis.db'

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# ——— 调试配置 ———
# 是否打开调试信息，默认为否
DEBUG_MODE = False

# ——— 日志配置 ———
# 是否按小时分割日志文件，默认为是
SPLIT_LOG_BY_HOUR = True

# ——— 1. 配置区域 ———
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
BACKTEST_DATE = '2025-08-01'

# V5.1 - 优化版本更新说明：
# 1. 新增持续性验证机制，过滤毛刺信号（观察池3-5分钟验证）
# 2. 重新定义主力纯度(PF)指标：70%<PF<150%健康区间，>150%出货风险
# 3. 增加成交量验证机制，实现价量资金三重共振（接口预留）
# 4. 优化信号报告系统，提供更精准的风险评估

# V5.1 - 成交量验证配置参数
# --------------------------------------------------
ENABLE_VOLUME_VALIDATION = False  # 成交量验证开关（当前数据无成交量字段，默认关闭）
VOLUME_MULTIPLIER_THRESHOLD = 1.5  # 成交量倍数验证阈值
VOLUME_FIELD_MAPPING = {
    '成交量': 'volume',
    'volume': 'volume', 
    '分钟成交量': 'volume',
    '换手率': 'turnover',
    'turnover': 'turnover',
    '分钟换手率': 'turnover'
}  # 成交量字段映射表，支持多种字段名

# V6 - 断层分析核心参数 (双重确认法)
# --------------------------------------------------
GAP_MIN_LEAD_THRESHOLD = 1.5
GAP_CONTEXT_MULTIPLIER = 1.2

# V6 - 资金加速度分析核心参数
# --------------------------------------------------
ACC_MIN_DELTA_THRESHOLD = 1.5e8
ACC_MIN_RATIO_THRESHOLD = 1.3
ACC_MIN_INFLOW_THRESHOLD = 3e8

# V8.0 - 优化后的动态阈值算法配置（基于涨停股全面分析）
# --------------------------------------------------
SLIDING_WINDOW_MINUTES = 30  # 滑动窗口时间长度（分钟）
WRA_PERCENTILE = 95  # WRA动态阈值百分位
CT_PERCENTILE = 95   # CT动态阈值百分位
WRA_MULTIPLIER = 1.0  # WRA阈值倍数 (降低33%: 1.5→1.0)
CT_MULTIPLIER = 1.2   # CT阈值倍数 (降低40%: 2.0→1.2)
MIN_RANK_THRESHOLD = 200  # 排名攻击区阈值 (扩大33%: 150→200)
MIN_PF_THRESHOLD = 0.6    # 主力意图明确阈值 (降低: 0.7→0.6)

# V8.0 优化：大幅降低能量验证参数
MIN_ABSOLUTE_INFLOW_CHANGE = 2e6  # 增量资金门槛：200万 (降低60%: 500万→200万)
MIN_RANK_BENCHMARK_RATIO = 0.2    # 点火后总额至少要达到第50名资金的20%

# V8.0 新增：早盘和板块特殊处理参数
EARLY_TRADING_END_TIME = "10:00"  # 早盘结束时间
EARLY_TRADING_MULTIPLIER = 0.7    # 早盘阈值倍数（降低30%）
HOT_SECTORS = ["软件开发", "半导体", "电网设备", "医疗器械"]  # 热门板块
HOT_SECTOR_MULTIPLIER = 0.8       # 热门板块阈值倍数（降低20%）

# --- 板块背离风险信号配置参数 ---
# --------------------------------------------------
MIN_CONSECUTIVE_DAYS_FOR_DIVERGENCE = 3  # 触发背离检测的最小连板数

# --- V11.0 潜伏板块识别核心参数 ---
# --------------------------------------------------
LURKING_SECTOR_MIN_LEAD_RATIO = 1.0  # 潜伏板块资金领先倍数 (N倍) - 降低标准便于发现
LURKING_SECTOR_MIN_STOCK_COUNT = 3   # 潜伏板块在Top50中的最少股票数 (M次)

# 主线强度评分系统配置
# UPDATE_CACHE_ON_STARTUP 已移除，不再使用akshare缓存

# --- 通用配置 ---
ANALYSIS_TOP_N = 10
MIN_SECTORS_FOR_ANALYSIS = 4

# --- V9.1 - 龙头评分体系配置 (升级版) ---
# --------------------------------------------------
INVALID_LEADER_SECTORS = {"银行", "保险", "证券", "中字头", "国字头"}
LEADERSHIP_SCORE_WEIGHTS_V9_4 = {
    'limit_up': 0.5,      # 情绪分权重 (与资金分平起平坐)
    'capital': 0.5        # 资金分权重 (与情绪分平起平坐)
}
SYNERGY_MULTIPLIER = 1.25   # 强强联合的“共振乘数” (不再是加分，而是乘法！)

# --- V9.5 龙头评分体系配置 (黄金法则版) ---
# --------------------------------------------------
# 将原有的权重配置注释掉或删除
# LEADERSHIP_SCORE_WEIGHTS_V9_4 = { ... }

# 新增V9.5权重配置，明确区分人气广度、高度和资金深度
LEADERSHIP_SCORE_WEIGHTS_V9_5 = {
    'breadth': 0.4,       # 人气广度 (涨停数量) 权重
    'height': 0.4,        # 人气高度 (最高连板) 权重
    'depth': 0.2          # 资金深度 (净流入额) 权重
}
BATTLEFIELD_SYNERGY_MULTIPLIER = 1.5 # 绝对主战场共振乘数 (从1.25提升至1.5)

# --- V10.2 主线评分增强配置 (尊重盘面现实) ---
# --------------------------------------------------
ABSOLUTE_MAINLINE_LIMIT_UP_THRESHOLD = 5  # 人气标准：板块涨停数达到此值，即认定为绝对主线
ABSOLUTE_MAINLINE_CAPITAL_RANK_THRESHOLD = 3  # 资金标准：板块资金排名进入此名次，即认定为绝对主线
CAPITAL_MAINLINE_MIN_LIMIT_UP_SUPPORT = 1  # 资金主线标准必须满足的最低涨停数
ABSOLUTE_MAINLINE_SCORE = 15.0  # 命中绝对主线后直接赋予的主线强度分

# --- V10.3 巨量资金主线增强配置 (资金断层) ---
# --------------------------------------------------
OVERWHELMING_CAPITAL_INFLOW_THRESHOLD = 5e8  # 5亿：必须超过这个绝对资金门槛
OVERWHELMING_CAPITAL_LEAD_RATIO = 1.5         # 1.5倍：必须是第二名资金的1.5倍以上

# --- 全局状态变量，用于存储上一分钟的数据快照 ---
previous_data_snapshot = {}

# --- V3.0 全局市场脉搏数据池 ---
market_pulse_data_pool = []  # 存储过去30分钟的WRA和CT值

# --- V5.1 持续性验证观察池 ---
observation_pool = {}  # 存储待观察的股票 {stock_name: observation_data}

# 移除了旧的缓存更新代码，现在直接使用数据库

def get_stock_sectors(stock_name, stock_code=None):
    """
    从stock_block_analysis.db数据库获取股票的概念、行业等映射关系

    参数:
    - stock_name: 股票名称
    - stock_code: 股票代码 (可选)

    返回:
    - dict: {'concepts': [], 'industries': []}
    """
    # 立即转换参数类型，避免pandas Series导致的布尔值歧义
    if stock_name is not None:
        try:
            stock_name = str(stock_name).strip()
        except:
            stock_name = ''
    else:
        stock_name = ''

    if stock_code is not None:
        try:
            stock_code = str(stock_code).strip()
        except:
            stock_code = ''
    else:
        stock_code = ''

    try:

        # 确定数据库文件路径，优先使用配置路径，不存在则使用备用路径
        db_file = STOCK_BLOCK_DB_PATH
        if not os.path.exists(db_file):
            db_file = STOCK_BLOCK_DB_FALLBACK
            if not os.path.exists(db_file):
                print(f"⚠️ 数据库文件不存在，已尝试路径：")
                print(f"   主路径: {STOCK_BLOCK_DB_PATH}")
                print(f"   备用路径: {STOCK_BLOCK_DB_FALLBACK}")
                return {'concepts': [], 'industries': []}

        concepts = []
        industries = []
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 如果有股票代码，优先使用股票代码查询
            if stock_code is not None and str(stock_code).strip():
                # 移除股票代码中的交易所后缀（如果有的话）
                clean_code = str(stock_code).strip()
                if '.' in clean_code:
                    clean_code = clean_code.split('.')[0]

                cursor.execute('''
                SELECT concept_blocks, industry_blocks, style_blocks, index_blocks
                FROM stocks
                WHERE stock_code = ?
                ''', (clean_code,))

                result = cursor.fetchone()
                if result:
                    concept_blocks, industry_blocks, style_blocks, index_blocks = result

                    # 解析概念板块信息 - 增强类型检查
                    if concept_blocks is not None and isinstance(concept_blocks, str) and concept_blocks.strip():
                        concepts = [c.strip() for c in concept_blocks.split(',') if isinstance(c, str) and c.strip()]

                    # 解析行业板块信息 - 从多个字段获取
                    # 1. 从industry_blocks字段获取
                    if industry_blocks is not None and isinstance(industry_blocks, str) and industry_blocks.strip():
                        industries.extend([i.strip() for i in industry_blocks.split(',') if isinstance(i, str) and i.strip()])

                    # 2. 从style_blocks字段获取（风格板块，包含一些行业信息）
                    if style_blocks is not None and isinstance(style_blocks, str) and style_blocks.strip():
                        style_items = [s.strip() for s in style_blocks.split(',') if isinstance(s, str) and s.strip()]
                        industries.extend(style_items)

                    # 3. 从index_blocks字段获取（指数板块，包含行业分类）
                    if index_blocks is not None and isinstance(index_blocks, str) and index_blocks.strip():
                        index_items = [idx.strip() for idx in index_blocks.split(',') if isinstance(idx, str) and idx.strip()]
                        industries.extend(index_items)
            
            # 如果通过股票代码没找到，或者没有提供股票代码，尝试通过股票名称查询
            if not concepts and not industries and stock_name is not None and str(stock_name).strip():
                cursor.execute('''
                SELECT concept_blocks, industry_blocks, style_blocks, index_blocks
                FROM stocks
                WHERE short_name = ? OR short_name LIKE ?
                ''', (stock_name, f'%{stock_name}%'))

                result = cursor.fetchone()
                if result:
                    concept_blocks, industry_blocks, style_blocks, index_blocks = result

                    # 解析概念板块信息 - 增强类型检查
                    if concept_blocks is not None and isinstance(concept_blocks, str) and concept_blocks.strip():
                        concepts = [c.strip() for c in concept_blocks.split(',') if isinstance(c, str) and c.strip()]

                    # 解析行业板块信息 - 从多个字段获取
                    # 1. 从industry_blocks字段获取
                    if industry_blocks is not None and isinstance(industry_blocks, str) and industry_blocks.strip():
                        industries.extend([i.strip() for i in industry_blocks.split(',') if isinstance(i, str) and i.strip()])

                    # 2. 从style_blocks字段获取（风格板块，包含一些行业信息）
                    if style_blocks is not None and isinstance(style_blocks, str) and style_blocks.strip():
                        style_items = [s.strip() for s in style_blocks.split(',') if isinstance(s, str) and s.strip()]
                        industries.extend(style_items)

                    # 3. 从index_blocks字段获取（指数板块，包含行业分类）
                    if index_blocks is not None and isinstance(index_blocks, str) and index_blocks.strip():
                        index_items = [idx.strip() for idx in index_blocks.split(',') if isinstance(idx, str) and idx.strip()]
                        industries.extend(index_items)
        
        # 如果数据库中没有找到数据，尝试从akshare数据文件中查找
        if not concepts and not industries and stock_code is not None and str(stock_code).strip():
            akshare_concepts, akshare_industries = _get_stock_sectors_from_akshare(stock_code)
            concepts.extend(akshare_concepts)
            industries.extend(akshare_industries)

        # 【【【新增：在数据源头统一过滤无意义概念，提升性能】】】
        filtered_concepts = filter_meaningful_concepts_and_sectors(concepts) if concepts else []

        return {
            'concepts': filtered_concepts,
            'industries': industries if industries else []
        }

    except Exception as e:
        print(f"从数据库获取股票{stock_name}板块信息失败: {e}")
        # 如果数据库查询失败，尝试从akshare数据文件中查找
        if stock_code is not None and str(stock_code).strip():
            try:
                akshare_concepts, akshare_industries = _get_stock_sectors_from_akshare(stock_code)
                # 【【【新增：在数据源头统一过滤无意义概念，提升性能】】】
                filtered_akshare_concepts = filter_meaningful_concepts_and_sectors(akshare_concepts[:5]) if akshare_concepts else []

                return {
                    'concepts': filtered_akshare_concepts,
                    'industries': akshare_industries[:3] if akshare_industries else []
                }
            except Exception as akshare_e:
                print(f"从akshare数据文件获取股票{stock_name}板块信息也失败: {akshare_e}")
        return {'concepts': [], 'industries': []}





def calculate_dynamic_weights(raw_capital_score, raw_emotion_score, current_time=None):
    """
    根据资金差距动态调整权重
    优化四：早盘阶段，资金权重优先
    """
    # 1. 计算资金差距系数
    capital_max = raw_capital_score.max()
    capital_median = raw_capital_score.median()

    # 2. 判断资金差距是否显著
    if capital_max > 0 and capital_median > 0:
        capital_ratio = capital_max / capital_median
        print(f"🔍 资金差距分析: 最大值{capital_max/1e8:.2f}亿 vs 中位数{capital_median/1e8:.2f}亿 = {capital_ratio:.1f}倍")

        # 动态权重计算
        if capital_ratio >= 10:
            # 10倍以上差距：极端情况，资金权重70%
            capital_weight = 0.7
            emotion_weight = 0.3
            print(f"📈 极端资金差距({capital_ratio:.1f}倍)，资金权重提升至70%")
        elif capital_ratio >= 5:
            # 5-10倍差距：显著差距，资金权重60-70%
            capital_weight = min(0.7, 0.6 + (capital_ratio - 5) * 0.02)
            emotion_weight = 1 - capital_weight
            print(f"📊 显著资金差距({capital_ratio:.1f}倍)，资金权重提升至{capital_weight:.0%}")
        elif capital_ratio >= 3:
            # 3-5倍差距：开始倾斜，资金权重55-60%
            capital_weight = 0.55 + (capital_ratio - 3) * 0.025
            emotion_weight = 1 - capital_weight
            print(f"⚖️ 中等资金差距({capital_ratio:.1f}倍)，资金权重调整至{capital_weight:.0%}")
        else:
            # 2-3倍差距：正常情况，保持平衡
            capital_weight = 0.5
            emotion_weight = 0.5
            print(f"🔄 正常资金差距({capital_ratio:.1f}倍)，维持50:50权重")
    else:
        # 无有效资金数据时，情绪为主
        capital_weight = 0.2
        emotion_weight = 0.8
        print(f"⚠️ 资金数据不足，情绪权重为主(80:20)")

    # 优化四：早盘阶段，资金权重优先
    if current_time is not None and current_time <= "09:45:00":
        capital_weight = max(capital_weight, 0.65)
        emotion_weight = 1 - capital_weight
        print(f"🌅 早盘阶段({current_time})，资金权重强制提升至{capital_weight:.0%}")

    return emotion_weight, capital_weight


def calculate_sector_leadership_score_v9_4(all_sectors_df, limit_up_stocks, current_time=None):
    """
    V9.4 - “帝王心法”龙头评分函数：引入共振乘数，平衡情绪与资金
    """
    try:
        if all_sectors_df is None or all_sectors_df.empty:
            return pd.DataFrame()

        result_df = all_sectors_df.copy()
        result_df['limit_up_count'] = 0
        result_df['max_consecutive'] = 0

        # 1. 统计涨停数据 (修复：统一使用涨停股池文件中的行业分类)
        if limit_up_stocks:
            sector_stats = {}
            for stock in limit_up_stocks:
                stock_name = stock.get('名称')
                stock_code = stock.get('代码')
                consecutive_days = stock.get('连板数', 1)
                try:
                    consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
                except:
                    consecutive_days = 1

                # 【修复】优先使用涨停股池文件中的行业信息，确保与涨停股池分析一致
                file_industry = stock.get('所属行业', '')

                # 获取数据库中的概念和行业信息作为补充
                sectors_info = get_stock_sectors(stock_name, stock_code)
                if sectors_info is None:
                    sectors_info = {'concepts': [], 'industries': []}

                # 构建完整的板块列表：概念 + 文件行业 + 数据库行业
                all_stock_sectors = sectors_info.get('concepts', [])
                if file_industry and file_industry != '未知':
                    all_stock_sectors.append(file_industry)
                # 添加数据库中的行业信息作为补充
                all_stock_sectors.extend(sectors_info.get('industries', []))

                for sector in set(all_stock_sectors):
                    if sector not in sector_stats:
                        sector_stats[sector] = {'limit_up_count': 0, 'max_consecutive': 0}
                    sector_stats[sector]['limit_up_count'] += 1
                    if consecutive_days > sector_stats[sector]['max_consecutive']:
                        sector_stats[sector]['max_consecutive'] = consecutive_days

            result_df.set_index('名称', inplace=True)
            for sector_name, stats in sector_stats.items():
                if sector_name in result_df.index:
                    # 更新已有板块的涨停数据
                    result_df.at[sector_name, 'limit_up_count'] = stats['limit_up_count']
                    result_df.at[sector_name, 'max_consecutive'] = stats['max_consecutive']
                else:
                    # 【修复】在数据补全时过滤无意义概念
                    meaningless_items = get_meaningless_items()
                    if sector_name in meaningless_items:
                        # 跳过无意义概念，不进行数据补全
                        continue

                    # 【V10.4 BUG修复】如果板块不存在（例如只有涨停数据没有资金数据），则新增该行
                    new_row_data = {
                        'limit_up_count': stats['limit_up_count'],
                        'max_consecutive': stats['max_consecutive'],
                        '今日主力净流入-净额': 0,  # 资金数据缺失，默认为0
                        'type': '概念'  # 默认为概念，类型影响较小
                    }
                    # 使用.loc来安全地新增行
                    result_df.loc[sector_name] = new_row_data
                    if DEBUG_MODE:
                        print(f"🔧 数据补全(V10.4): 板块【{sector_name}】仅在涨停池发现，已补全数据用于评分。")

            result_df.reset_index(inplace=True)
            # 补全后，部分列可能为NaN，需要填充
            result_df.fillna(0, inplace=True)

        # 2. 【第一步：重新定义原始分数的计算方式】
        # 情绪分：融合涨停数和连板数，连板数作为强力乘数
        # 优化二：削弱"连板高度"的绝对影响力 - 改为非线性影响
        raw_emotion_score = np.log1p(result_df['limit_up_count']) * np.log1p(1 + result_df['max_consecutive'])

        # 资金分：彻底放弃对数压缩，直接使用原始资金净流入值
        raw_capital_score = result_df['今日主力净流入-净额'].clip(lower=0)

        # 【第二步：变革归一化流程】
        def min_max_scale(series):
            if series.max() > series.min():
                return (series - series.min()) / (series.max() - series.min())
            return pd.Series(0.0, index=series.index)

        # 对新的原始分数进行归一化
        limit_up_score = min_max_scale(raw_emotion_score)  # 内涵已变为"情绪分"
        capital_score = min_max_scale(raw_capital_score)

        # 【第三步：动态资金权重放大机制】
        # 计算动态权重，传入时间参数用于早盘权重优化
        current_time_str = None
        if current_time is not None:
            if hasattr(current_time, 'strftime'):
                current_time_str = current_time.strftime('%H:%M:%S')
            else:
                current_time_str = str(current_time)
        emotion_weight, capital_weight = calculate_dynamic_weights(raw_capital_score, raw_emotion_score, current_time_str)

        # 使用动态权重计算基础分
        result_df['leadership_score'] = (
            limit_up_score * emotion_weight +
            capital_score * capital_weight
        )

        # 优化三：引入"资金与情绪的共振"校验 - 惩罚"偏科"严重的板块
        # 如果资金分低于0.1但情绪分高于0.7，则惩罚
        penalty_mask = (capital_score < 0.1) & (limit_up_score > 0.7)
        result_df.loc[penalty_mask, 'leadership_score'] *= 0.5

        # 4. 【【【 V9.4 核心改造点： “帝王加冕”之共振乘数 】】】
        # 筛选出同时在情绪和资金上都表现优异的"双优生"
        synergy_mask = (limit_up_score > 0.7) & (capital_score > 0.7)
        result_df.loc[synergy_mask, 'leadership_score'] *= SYNERGY_MULTIPLIER

        # 5. 对护盘板块进行最终惩罚
        # 修正：使用正确的变量名 INVALID_LEADER_SECTORS
        penalty_mask = result_df['名称'].isin(INVALID_LEADER_SECTORS)
        result_df.loc[penalty_mask, 'leadership_score'] *= 0.1

        # 优化一："负向资金"一票否决权 - 对资金净流出为负的板块进行惩罚
        negative_flow_mask = result_df['今日主力净流入-净额'] < 0
        result_df.loc[negative_flow_mask, 'leadership_score'] *= 0.1

        # 6. 最后再次归一化，让最高分为1
        if result_df['leadership_score'].max() > 0:
            result_df['leadership_score'] = result_df['leadership_score'] / result_df['leadership_score'].max()

        return result_df

    except Exception as e:
        print(f"❌ V9.4龙头评分计算失败: {e}")
        import traceback
        traceback.print_exc()
        return all_sectors_df.copy() if all_sectors_df is not None else pd.DataFrame()


def calculate_sector_leadership_score_v9_5(all_sectors_df, limit_up_stocks, current_time=None):
    """
    V9.5 - "黄金法则"龙头评分函数：人气广度 + 人气高度 + 资金深度
    """
    try:
        if all_sectors_df is None or all_sectors_df.empty:
            return pd.DataFrame()

        result_df = all_sectors_df.copy()
        result_df['limit_up_count'] = 0
        result_df['max_consecutive'] = 0

        # 1. 统计涨停数据 (与v9.4版本相同的涨停统计逻辑)
        if limit_up_stocks:
            sector_stats = {}
            for stock in limit_up_stocks:
                stock_name = stock.get('名称')
                stock_code = stock.get('代码')
                consecutive_days = stock.get('连板数', 1)
                try:
                    consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
                except:
                    consecutive_days = 1

                # 【修复】优先使用涨停股池文件中的行业信息，确保与涨停股池分析一致
                file_industry = stock.get('所属行业', '')

                # 获取数据库中的概念和行业信息作为补充
                sectors_info = get_stock_sectors(stock_name, stock_code)
                if sectors_info is None:
                    sectors_info = {'concepts': [], 'industries': []}

                # 构建完整的板块列表：概念 + 文件行业 + 数据库行业
                all_stock_sectors = sectors_info.get('concepts', [])
                if file_industry and file_industry != '未知':
                    all_stock_sectors.append(file_industry)
                # 添加数据库中的行业信息作为补充
                all_stock_sectors.extend(sectors_info.get('industries', []))

                for sector in set(all_stock_sectors):
                    if sector not in sector_stats:
                        sector_stats[sector] = {'limit_up_count': 0, 'max_consecutive': 0}
                    sector_stats[sector]['limit_up_count'] += 1
                    if consecutive_days > sector_stats[sector]['max_consecutive']:
                        sector_stats[sector]['max_consecutive'] = consecutive_days

            result_df.set_index('名称', inplace=True)
            for sector_name, stats in sector_stats.items():
                if sector_name in result_df.index:
                    # 更新已有板块的涨停数据
                    result_df.at[sector_name, 'limit_up_count'] = stats['limit_up_count']
                    result_df.at[sector_name, 'max_consecutive'] = stats['max_consecutive']
                else:
                    # 【修复】在数据补全时过滤无意义概念
                    meaningless_items = get_meaningless_items()
                    if sector_name in meaningless_items:
                        # 跳过无意义概念，不进行数据补全
                        continue

                    # 【V10.4 BUG修复】如果板块不存在（例如只有涨停数据没有资金数据），则新增该行
                    new_row_data = {
                        'limit_up_count': stats['limit_up_count'],
                        'max_consecutive': stats['max_consecutive'],
                        '今日主力净流入-净额': 0,  # 资金数据缺失，默认为0
                        'type': '概念'  # 默认为概念，类型影响较小
                    }
                    # 使用.loc来安全地新增行
                    result_df.loc[sector_name] = new_row_data
                    if DEBUG_MODE:
                        print(f"🔧 数据补全(V10.4): 板块【{sector_name}】仅在涨停池发现，已补全数据用于评分。")

            result_df.reset_index(inplace=True)
            # 补全后，部分列可能为NaN，需要填充
            result_df.fillna(0, inplace=True)

        # 2. 【V9.5 核心改造点：定义三维原始分】
        # 放弃对数平滑，直接使用原始值，更能体现差距
        raw_breadth_score = result_df['limit_up_count']                  # 人气广度分
        raw_height_score = result_df['max_consecutive']                  # 人气高度分
        raw_depth_score = result_df['今日主力净流入-净额'].clip(lower=0) # 资金深度分

        # 3. 【V9.5 核心改造点：三维独立归一化】
        def min_max_scale(series):
            # 增加对空Series或最大最小值相同情况的处理
            if series.empty or series.max() == series.min():
                return pd.Series(0.0, index=series.index)
            return (series - series.min()) / (series.max() - series.min())

        breadth_score = min_max_scale(raw_breadth_score)
        height_score = min_max_scale(raw_height_score)
        depth_score = min_max_scale(raw_depth_score)

        # 4. 【V9.5 核心改造点：应用全新固定权重】
        weights = LEADERSHIP_SCORE_WEIGHTS_V9_5
        result_df['leadership_score'] = (
            breadth_score * weights['breadth'] +
            height_score * weights['height'] +
            depth_score * weights['depth']
        )

        # 5. 【V9.5 核心改造点："绝对主战场"共振暴击】
        # 定义主战场标准：人气广度和高度同时排进市场前20%
        breadth_threshold = raw_breadth_score.quantile(0.8)
        height_threshold = raw_height_score.quantile(0.8)

        # 找出同时满足两个条件的板块
        # 确保阈值大于0，避免无涨停板块被误选
        if breadth_threshold > 0 and height_threshold > 0:
            battlefield_mask = (raw_breadth_score >= breadth_threshold) & (raw_height_score >= height_threshold)
            result_df.loc[battlefield_mask, 'leadership_score'] *= BATTLEFIELD_SYNERGY_MULTIPLIER

            # 在日志中明确打印出触发共振的板块
            if battlefield_mask.any():
                battlefield_sectors = result_df[battlefield_mask]['名称'].tolist()
                print(f"⚔️ 绝对主战场共振暴击: 板块【{', '.join(battlefield_sectors)}】因人气广度与高度双优，评分乘以{BATTLEFIELD_SYNERGY_MULTIPLIER}倍！")

        # 6. 【保留】对护盘板块和负向资金进行惩罚
        penalty_mask = result_df['名称'].isin(INVALID_LEADER_SECTORS)
        result_df.loc[penalty_mask, 'leadership_score'] *= 0.1

        negative_flow_mask = result_df['今日主力净流入-净额'] < 0
        result_df.loc[negative_flow_mask, 'leadership_score'] *= 0.1

        # 7. 【保留】最后再次归一化，让最高分为1
        if result_df['leadership_score'].max() > 0:
            result_df['leadership_score'] = result_df['leadership_score'] / result_df['leadership_score'].max()

        return result_df

    except Exception as e:
        print(f"❌ V9.5龙头评分计算失败: {e}")
        import traceback
        traceback.print_exc()
        return all_sectors_df.copy() if all_sectors_df is not None else pd.DataFrame()


# 【【【全局变量：内存缓存历史排名数据】】】
_ranking_history_cache = {}  # 格式: {timestamp: {stock_name: rank}}
_cache_start_time = None     # 缓存开始时间（开盘时间）


def display_ranking_changes(current_rankings, current_sim_time, data_dir):
    """显示排名变化最大的股票 - 内存缓存优化版"""
    from datetime import datetime, timedelta

    try:
        # 更新当前排名到缓存
        _update_ranking_cache(current_rankings, current_sim_time)

        # 解析当前时间 - 处理字符串和time对象两种情况
        if isinstance(current_sim_time, str):
            current_time = datetime.strptime(current_sim_time, '%H:%M:%S')
        else:
            # 如果是datetime.time对象，转换为datetime对象
            current_time = datetime.combine(datetime.today(), current_sim_time)

        # 定义时间段和对应的查找策略
        time_periods = [
            ('上一次', timedelta(minutes=1)),
            ('上15分钟', timedelta(minutes=15)),
            ('上30分钟', timedelta(minutes=30)),
            ('上1小时', timedelta(hours=1)),
            ('开盘以来', None)  # 特殊处理
        ]

        print("排名变化分析:")

        for period_name, time_delta in time_periods:
            try:
                if period_name == '开盘以来':
                    # 开盘以来：使用缓存中最早的数据
                    historical_rankings = _get_earliest_ranking_from_cache()
                    if historical_rankings and len(_ranking_history_cache) > 1:
                        # 计算排名变化
                        ranking_changes = _calculate_ranking_changes_fast(current_rankings, historical_rankings)
                        _display_top_changes(ranking_changes, period_name)
                    else:
                        print(f"  与{period_name}比: 数据积累中...")
                else:
                    # 其他时间段：查找最接近的历史数据
                    target_time = current_time - time_delta
                    historical_rankings = _get_closest_ranking_from_cache(target_time)

                    if historical_rankings:
                        # 计算排名变化
                        ranking_changes = _calculate_ranking_changes_fast(current_rankings, historical_rankings)
                        _display_top_changes(ranking_changes, period_name)
                    else:
                        # 尝试从文件中获取历史数据（作为备选方案）
                        file_rankings = _get_historical_rankings_from_file(target_time, data_dir, period_name)
                        if file_rankings:
                            ranking_changes = _calculate_ranking_changes_fast(current_rankings, file_rankings)
                            _display_top_changes(ranking_changes, period_name)
                        else:
                            print(f"  与{period_name}比: 数据积累中...")

            except Exception as e:
                print(f"  与{period_name}比: 计算失败")

    except Exception as e:
        print(f"排名变化分析失败: {e}")


def _display_top_changes(ranking_changes, period_name):
    """显示排名变化最大的股票"""
    if ranking_changes:
        # 过滤出有实际变化的股票
        significant_changes = [change for change in ranking_changes if abs(change[2]) > 0]

        if significant_changes:
            # 按变化幅度排序，取前5名
            top_changes = sorted(significant_changes, key=lambda x: abs(x[2]), reverse=True)[:5]
            change_display = " | ".join([
                f"{stock_name} {old_rank}->{new_rank}"
                for stock_name, old_rank, change, new_rank in top_changes
            ])
            print(f"  与{period_name}比: {change_display}")
        else:
            print(f"  与{period_name}比: 排名无变化")
    else:
        print(f"  与{period_name}比: 无对比数据")


def _get_historical_rankings_from_file(target_time, data_dir, period_name):
    """从文件中获取历史排名数据（备选方案）"""
    import os
    import glob
    import pandas as pd
    from datetime import datetime

    try:
        # 查找个股资金流文件
        pattern = os.path.join(data_dir, "*个股资金流*.csv")
        files = glob.glob(pattern)

        if not files:
            return None

        # 找到最接近目标时间的文件
        target_file = None
        min_time_diff = float('inf')

        for file_path in files:
            try:
                filename = os.path.basename(file_path)
                # 提取时间戳，例如：个股资金流_20250730_093000.csv
                if '_' in filename:
                    parts = filename.split('_')
                    if len(parts) >= 3:
                        time_str = parts[2].replace('.csv', '')
                        if len(time_str) == 6:  # HHMMSS格式
                            file_time = datetime.strptime(time_str, '%H%M%S').time()
                            file_datetime = datetime.combine(datetime.today(), file_time)

                            # 确保不使用未来数据
                            current_datetime = datetime.combine(datetime.today(), target_time.time())
                            if file_datetime <= current_datetime:
                                time_diff = abs((target_time - file_datetime).total_seconds())
                                if time_diff < min_time_diff:
                                    min_time_diff = time_diff
                                    target_file = file_path
            except:
                continue

        if target_file and min_time_diff < 1800:  # 30分钟内的文件才有效
            # 读取历史文件并提取排名
            df = pd.read_csv(target_file, encoding='utf-8')
            if not df.empty and '名称' in df.columns:
                # 应用相同的过滤逻辑
                from dynamic_gap_detector import apply_stock_filter, classify_file_type
                file_format = classify_file_type(os.path.basename(target_file))
                filtered_df = apply_stock_filter(df, file_format)

                if not filtered_df.empty:
                    historical_rankings = {}
                    for idx, row in filtered_df.head(50).iterrows():
                        stock_name = row['名称']
                        historical_rank = idx + 1
                        historical_rankings[stock_name] = historical_rank
                    return historical_rankings

        return None

    except Exception:
        return None


def _update_ranking_cache(current_rankings, current_sim_time):
    """更新排名缓存"""
    from datetime import datetime, timedelta
    global _ranking_history_cache, _cache_start_time

    try:
        # 解析时间 - 处理字符串和time对象两种情况
        if isinstance(current_sim_time, str):
            current_time = datetime.strptime(current_sim_time, '%H:%M:%S')
        else:
            # 如果是datetime.time对象，转换为datetime对象
            current_time = datetime.combine(datetime.today(), current_sim_time)

        # 设置缓存开始时间（第一次调用时）
        if _cache_start_time is None:
            _cache_start_time = current_time

        # 将当前排名添加到缓存
        _ranking_history_cache[current_time] = current_rankings.copy()

        # 清理过期数据（保留最近2小时的数据）
        cutoff_time = current_time - timedelta(hours=2)
        expired_times = [t for t in _ranking_history_cache.keys() if t < cutoff_time]
        for expired_time in expired_times:
            del _ranking_history_cache[expired_time]

    except Exception:
        pass  # 静默处理错误，不影响主流程


def _get_closest_ranking_from_cache(target_time):
    """从缓存中获取最接近目标时间的排名数据"""
    global _ranking_history_cache

    if not _ranking_history_cache:
        return None

    # 找到最接近的时间点
    closest_time = None
    min_diff = float('inf')

    for cached_time in _ranking_history_cache.keys():
        time_diff = abs((target_time - cached_time).total_seconds())
        if time_diff < min_diff:
            min_diff = time_diff
            closest_time = cached_time

    # 如果时间差超过5分钟，认为数据不可靠
    if closest_time and min_diff <= 300:  # 5分钟内
        return _ranking_history_cache[closest_time]

    return None


def _get_earliest_ranking_from_cache():
    """从缓存中获取最早的排名数据（开盘以来）"""
    global _ranking_history_cache

    if not _ranking_history_cache:
        return None

    earliest_time = min(_ranking_history_cache.keys())
    return _ranking_history_cache[earliest_time]


def _calculate_ranking_changes_fast(current_rankings, historical_rankings):
    """快速计算排名变化"""
    ranking_changes = []

    for stock_name, current_rank in current_rankings.items():
        if stock_name in historical_rankings:
            historical_rank = historical_rankings[stock_name]
            change = historical_rank - current_rank  # 正数表示排名上升
            ranking_changes.append((stock_name, historical_rank, change, current_rank))

    return ranking_changes


def detect_divergence_signals(limit_up_stocks, all_sectors_df):
    """
    板块背离风险信号检测函数

    检测逻辑：
    当一个达到特定连板高度（例如≥3板）的龙头股，其所属的板块（行业）当天的资金却是净流出时，
    这构成一个强烈的风险信号。

    参数:
    - limit_up_stocks: 当前涨停股池的列表
    - all_sectors_df: 包含所有板块及其资金流数据的Pandas DataFrame

    返回:
    - dict: 键为存在背离风险的股票名称，值为具体的风险描述
    """
    divergence_warnings = {}

    if not limit_up_stocks or all_sectors_df is None or all_sectors_df.empty:
        return divergence_warnings

    try:
        # 遍历涨停股池
        for stock in limit_up_stocks:
            stock_name = stock.get('名称', '')
            if not stock_name:
                continue

            # 获取连板数
            consecutive_days = stock.get('连板数', 1)
            try:
                consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
            except:
                consecutive_days = 1

            # 筛选出连板数大于等于3天的股票
            if consecutive_days < MIN_CONSECUTIVE_DAYS_FOR_DIVERGENCE:
                continue

            # 获取该股票的所属行业
            industry = stock.get('所属行业', '')
            if not industry or industry == '未知':
                continue

            # 在 all_sectors_df 中查找该行业的今日主力净流入-净额
            industry_mask = (all_sectors_df['名称'] == industry) & (all_sectors_df['type'] == '行业')
            matching_sectors = all_sectors_df.loc[industry_mask]

            if not matching_sectors.empty:
                sector_row = matching_sectors.iloc[0]
                net_inflow = sector_row.get('今日主力净流入-净额', 0)

                # 如果净流入额小于0，则记录该股票名称
                if net_inflow < 0:
                    # 格式化金额显示（显示流出金额为正数）
                    abs_amount = abs(net_inflow)
                    if abs_amount >= 1e8:
                        amount_str = f"{abs_amount / 1e8:.2f}亿"
                    elif abs_amount >= 1e4:
                        amount_str = f"{abs_amount / 1e4:.2f}万"
                    else:
                        amount_str = f"{abs_amount:.2f}"
                    divergence_warnings[stock_name] = f"板块({industry})资金净流出 {amount_str}"

                    if DEBUG_MODE:
                        print(f"🚨 发现板块背离风险: 【{stock_name}】{consecutive_days}连板，但所属行业【{industry}】净流出{amount_str}")

        return divergence_warnings

    except Exception as e:
        print(f"❌ 板块背离风险信号检测失败: {e}")
        return divergence_warnings


def _get_stock_sectors_from_akshare(stock_code):
    """
    从akshare数据文件中获取股票的概念和行业信息

    参数:
    - stock_code: 股票代码

    返回:
    - tuple: (concepts_list, industries_list)
    """
    import json

    concepts = []
    industries = []

    try:
        # 处理股票代码格式
        stock_code_str = str(stock_code).strip()
        if not stock_code_str:
            return concepts, industries

        # 确保股票代码包含交易所后缀
        if '.' not in stock_code_str:
            # 根据代码前缀添加交易所后缀
            if stock_code_str.startswith('60') or stock_code_str.startswith('68'):
                stock_code_str = f"{stock_code_str}.SH"
            elif stock_code_str.startswith('00') or stock_code_str.startswith('30'):
                stock_code_str = f"{stock_code_str}.SZ"
            elif stock_code_str.startswith('8'):
                stock_code_str = f"{stock_code_str}.BJ"

        # 读取概念数据
        concept_file = os.path.join('akshare_data', 'ak_stock_concept_map.json')
        if os.path.exists(concept_file):
            with open(concept_file, 'r', encoding='utf-8') as f:
                concept_data = json.load(f)
                if stock_code_str in concept_data:
                    concepts = concept_data[stock_code_str]

        # 读取行业数据
        industry_file = os.path.join('akshare_data', 'ak_stock_industry_map.json')
        if os.path.exists(industry_file):
            with open(industry_file, 'r', encoding='utf-8') as f:
                industry_data = json.load(f)
                if stock_code_str in industry_data:
                    industries = industry_data[stock_code_str]

    except Exception as e:
        print(f"从akshare数据文件读取股票{stock_code_str}信息失败: {e}")

    return concepts, industries


# V7.1 - 新增："协同信号"量化验证配置参数
# --------------------------------------------------
MIN_SYNERGY_INFLOW_DELTA = 3e7  # 协同信号要求的最小资金增量 (例如: 3000万)
MIN_SYNERGY_RANK_JUMP = 20      # 协同信号要求的最小排名跃升 (例如: 20位)

def update_synergy_thresholds(min_inflow_delta=None, min_rank_jump=None):
    """动态更新协同信号阈值的配置函数"""
    global MIN_SYNERGY_INFLOW_DELTA, MIN_SYNERGY_RANK_JUMP
    if min_inflow_delta is not None:
        MIN_SYNERGY_INFLOW_DELTA = min_inflow_delta
        print(f"[配置更新] 协同信号最小资金增量阈值: {format_amount(min_inflow_delta)}")
    if min_rank_jump is not None:
        MIN_SYNERGY_RANK_JUMP = min_rank_jump
        print(f"[配置更新] 协同信号最小排名跃升阈值: {min_rank_jump} 位")

# V7 - 新增：“持续攻击信号”配置参数
# --------------------------------------------------
SUSTAINED_ATTACK_WINDOW_MINUTES = 45 # 持续攻击信号的分析时间窗口（分钟）
SUSTAINED_RANK_THRESHOLD = 200       # 排名必须进入该区域内才开始分析
SUSTAINED_CUMULATIVE_INFLOW_RANK_BENCHMARK = 30 # 窗口期资金净增量必须超过当日第N名的总净流入
SUSTAINED_INFLOW_STABILITY_RATIO = 0.7 # 窗口期内，资金流入为正的时间点占比必须高于此阈值
SUSTAINED_RANK_SLOPE_THRESHOLD = -0.5  # 排名趋势线斜率必须小于该值（负数，代表排名在提升）
SUSTAINED_MIN_DATA_POINTS = 5          # 窗口期内至少需要N个数据点才进行分析
class MarketPulseDataPool:
    """V3.0 市场脉搏实时数据池 - 滑动窗口管理器"""

    def __init__(self, window_minutes=30):
        self.window_minutes = window_minutes
        self.data_pool = []  # 存储格式: {'timestamp': datetime, 'stock_name': str, 'wra': float, 'ct': float, 'rank': int}

    def add_data_point(self, timestamp, stock_name, wra, ct, rank):
        """添加数据点到市场脉搏数据池"""
        if rank <= 500:  # 只存储排名前500的有意义变化
            self.data_pool.append({
                'timestamp': timestamp,
                'stock_name': stock_name,
                'wra': wra,
                'ct': ct,
                'rank': rank
            })

    def cleanup_old_data(self, current_time):
        """清理超过滑动窗口的旧数据"""
        from datetime import timedelta, datetime, date

        # 修复时间计算：统一处理datetime和time对象
        if isinstance(current_time, datetime):
            current_dt = current_time
        else:
            current_dt = datetime.combine(date.today(), current_time)

        cutoff_datetime = current_dt - timedelta(minutes=self.window_minutes)
        cutoff_time = cutoff_datetime.time()

        # 过滤数据时也要统一时间类型
        filtered_data = []
        for data in self.data_pool:
            data_timestamp = data['timestamp']
            if isinstance(data_timestamp, datetime):
                data_time = data_timestamp.time()
            else:
                data_time = data_timestamp

            if data_time >= cutoff_time:
                filtered_data.append(data)

        self.data_pool = filtered_data

    def get_dynamic_thresholds(self, current_market_data=None, current_time=None):
        """V9.0 完全自适应的动态阈值计算 - 基于当天实际市场情况"""
        if len(self.data_pool) < 10:  # 数据不足时使用市场化默认值
            return self._calculate_market_based_fallback_thresholds(current_market_data)

        import numpy as np
        from datetime import datetime

        # 基础历史数据阈值
        wra_values = [data['wra'] for data in self.data_pool if data['wra'] > 0]
        ct_values = [data['ct'] for data in self.data_pool if data['ct'] > 0]

        if not wra_values or not ct_values:
            return self._calculate_market_based_fallback_thresholds(current_market_data)

        # 1. 基于历史数据的基础阈值
        base_wra_threshold = np.percentile(wra_values, WRA_PERCENTILE)
        base_ct_threshold = np.percentile(ct_values, CT_PERCENTILE)

        # 2. 基于当前市场的自适应阈值计算
        adaptive_thresholds = self._calculate_adaptive_thresholds(current_market_data, current_time)

        # 3. 融合历史和当前市场数据
        final_wra_threshold = min(base_wra_threshold, adaptive_thresholds['adaptive_wra_threshold'])
        final_ct_threshold = min(base_ct_threshold, adaptive_thresholds['adaptive_ct_threshold'])

        return {
            'dynamic_wra_threshold': final_wra_threshold,
            'dynamic_ct_threshold': final_ct_threshold,
            'data_points': len(self.data_pool),
            'wra_samples': len(wra_values),
            'ct_samples': len(ct_values),
            'market_based_ct_threshold': adaptive_thresholds['market_ct_threshold'],
            'adaptive_inflow_threshold': adaptive_thresholds['adaptive_inflow_threshold'],
            'market_activity_level': adaptive_thresholds['market_activity_level'],
            'time_factor': adaptive_thresholds['time_factor'],
            'current_market_data': current_market_data  # V10.0 新增：传递市场数据用于板块分析
        }

    def _calculate_market_based_fallback_thresholds(self, current_market_data):
        """当历史数据不足时，基于当前市场数据计算备用阈值"""
        if current_market_data is None or len(current_market_data) < 10:
            return {
                'dynamic_wra_threshold': 0.05,
                'dynamic_ct_threshold': 500,
                'market_based_ct_threshold': 300,
                'adaptive_inflow_threshold': 1e6,
                'market_activity_level': 'unknown',
                'time_factor': 1.0,
                'data_points': 0
            }

        # 基于当前市场前50名计算基础阈值
        top50_inflows = current_market_data.head(50)['今日主力净流入-净额'].tolist()

        # CT阈值：基于资金流差距
        if len(top50_inflows) >= 10:
            gaps = [abs(top50_inflows[i] - top50_inflows[i+1]) for i in range(9)]
            avg_gap = sum(gaps) / len(gaps)
            ct_threshold = max(avg_gap * 0.2 / 1e4, 100)  # 万元/分钟，最低100万
        else:
            ct_threshold = 300

        # WRA阈值：基于排名分布
        wra_threshold = 0.02  # 保守的基础值

        # 增量资金阈值：基于第50名资金的10%
        if len(top50_inflows) >= 50:
            inflow_threshold = max(top50_inflows[49] * 0.1, 5e5)  # 最低50万
        else:
            inflow_threshold = 1e6

        return {
            'dynamic_wra_threshold': wra_threshold,
            'dynamic_ct_threshold': ct_threshold,
            'market_based_ct_threshold': ct_threshold,
            'adaptive_inflow_threshold': inflow_threshold,
            'market_activity_level': 'fallback',
            'time_factor': 1.0,
            'data_points': 0
        }

    def _calculate_adaptive_thresholds(self, current_market_data, current_time):
        """V9.0 核心自适应阈值计算算法"""
        import numpy as np
        from datetime import datetime, time

        if current_market_data is None or len(current_market_data) < 20:
            return self._get_default_adaptive_thresholds()

        # 1. 市场活跃度分析
        market_activity = self._analyze_market_activity(current_market_data)

        # 2. 时间因子计算
        time_factor = self._calculate_time_factor(current_time)

        # 3. 基于市场分布的CT阈值
        market_ct_threshold = self._calculate_market_ct_threshold(current_market_data, market_activity)

        # 4. 基于排名竞争的WRA阈值
        adaptive_wra_threshold = self._calculate_adaptive_wra_threshold(current_market_data, market_activity)

        # 5. 基于资金分布的增量阈值
        adaptive_inflow_threshold = self._calculate_adaptive_inflow_threshold(current_market_data, market_activity)

        # 6. 应用时间因子调整
        final_ct_threshold = market_ct_threshold * time_factor
        final_wra_threshold = adaptive_wra_threshold * time_factor
        final_inflow_threshold = adaptive_inflow_threshold * time_factor

        return {
            'market_ct_threshold': final_ct_threshold,
            'adaptive_ct_threshold': final_ct_threshold,
            'adaptive_wra_threshold': final_wra_threshold,
            'adaptive_inflow_threshold': final_inflow_threshold,
            'market_activity_level': market_activity['level'],
            'time_factor': time_factor
        }

    def _analyze_market_activity(self, current_market_data):
        """分析当前市场活跃度"""
        import numpy as np

        # 计算前100名的资金流入分布
        top100_inflows = current_market_data.head(100)['今日主力净流入-净额'].tolist()

        # 活跃度指标
        total_inflow = sum([x for x in top100_inflows if x > 0])
        positive_count = len([x for x in top100_inflows if x > 0])
        avg_inflow = total_inflow / max(positive_count, 1)

        # 资金集中度（前10名占前100名的比例）
        top10_inflow = sum(top100_inflows[:10])
        concentration = top10_inflow / max(total_inflow, 1) if total_inflow > 0 else 0

        # 判断活跃度等级
        if avg_inflow > 5e7 and positive_count > 60:  # 平均5000万以上，60只以上正流入
            level = 'very_high'
            multiplier = 0.6  # 高活跃度时降低阈值
        elif avg_inflow > 2e7 and positive_count > 40:  # 平均2000万以上，40只以上正流入
            level = 'high'
            multiplier = 0.7
        elif avg_inflow > 1e7 and positive_count > 20:  # 平均1000万以上，20只以上正流入
            level = 'medium'
            multiplier = 0.8
        else:
            level = 'low'
            multiplier = 1.0  # 低活跃度时保持原阈值

        return {
            'level': level,
            'multiplier': multiplier,
            'avg_inflow': avg_inflow,
            'positive_count': positive_count,
            'concentration': concentration,
            'total_inflow': total_inflow
        }

    def _calculate_time_factor(self, current_time):
        """计算时间因子，早盘和尾盘降低阈值"""
        if current_time is None:
            return 1.0

        try:
            if isinstance(current_time, str):
                time_str = current_time
            else:
                time_str = current_time.strftime("%H:%M")

            hour, minute = map(int, time_str.split(':'))
            time_minutes = hour * 60 + minute

            # 早盘时段 (09:30-10:30): 降低阈值30%
            if 570 <= time_minutes <= 630:  # 09:30-10:30
                return 0.7
            # 午后开盘 (13:00-14:00): 降低阈值20%
            elif 780 <= time_minutes <= 840:  # 13:00-14:00
                return 0.8
            # 尾盘时段 (14:30-15:00): 降低阈值25%
            elif 870 <= time_minutes <= 900:  # 14:30-15:00
                return 0.75
            else:
                return 1.0
        except:
            return 1.0

    def _calculate_market_ct_threshold(self, current_market_data, market_activity):
        """基于市场资金流分布计算CT阈值"""
        import numpy as np

        # 获取前50名的资金流入数据
        top50_inflows = current_market_data.head(50)['今日主力净流入-净额'].tolist()

        if len(top50_inflows) < 10:
            return 300  # 默认300万/分钟

        # 计算相邻排名间的资金差距
        gaps = []
        for i in range(min(20, len(top50_inflows)-1)):
            gap = abs(top50_inflows[i] - top50_inflows[i+1])
            if gap > 0:
                gaps.append(gap)

        if not gaps:
            return 300

        # 使用中位数差距的20%作为基础CT阈值
        median_gap = np.median(gaps)
        base_ct_threshold = median_gap * 0.2 / 1e4  # 转换为万元/分钟

        # 应用市场活跃度调整
        adjusted_ct_threshold = base_ct_threshold * market_activity['multiplier']

        # 设置合理的范围：50万-2000万/分钟
        return max(50, min(2000, adjusted_ct_threshold))

    def _calculate_adaptive_wra_threshold(self, current_market_data, market_activity):
        """基于排名竞争激烈程度计算WRA阈值"""
        # 基础WRA阈值：根据市场活跃度调整
        base_wra_threshold = 0.05  # 基础5%的排名改善要求

        # 根据市场活跃度调整
        if market_activity['level'] == 'very_high':
            # 高活跃度市场，排名变化更频繁，降低要求
            adjusted_wra_threshold = base_wra_threshold * 0.4  # 降低60%
        elif market_activity['level'] == 'high':
            adjusted_wra_threshold = base_wra_threshold * 0.6  # 降低40%
        elif market_activity['level'] == 'medium':
            adjusted_wra_threshold = base_wra_threshold * 0.8  # 降低20%
        else:
            adjusted_wra_threshold = base_wra_threshold  # 保持原值

        # 设置合理范围：0.01-0.15
        return max(0.01, min(0.15, adjusted_wra_threshold))

    def _calculate_adaptive_inflow_threshold(self, current_market_data, market_activity):
        """基于市场资金分布计算增量资金阈值"""
        # 获取第100名的资金流入作为基准
        if len(current_market_data) >= 100:
            benchmark_inflow = current_market_data.iloc[99]['今日主力净流入-净额']
            # 增量资金阈值设为第100名资金的10%
            base_threshold = max(abs(benchmark_inflow) * 0.1, 5e5)  # 最低50万
        else:
            base_threshold = 1e6  # 默认100万

        # 根据市场活跃度调整
        adjusted_threshold = base_threshold * market_activity['multiplier']

        # 设置合理范围：50万-5000万
        return max(5e5, min(5e7, adjusted_threshold))

    def _get_default_adaptive_thresholds(self):
        """获取默认的自适应阈值"""
        return {
            'market_ct_threshold': 200,
            'adaptive_ct_threshold': 200,
            'adaptive_wra_threshold': 0.03,
            'adaptive_inflow_threshold': 1e6,
            'market_activity_level': 'unknown',
            'time_factor': 1.0
        }



    def _get_stock_sector_info(self, stock_name, stock_code=None):
        """获取股票的板块信息"""
        if not hasattr(self, 'stock_board_mapping') or self.stock_board_mapping is None:
            self.stock_board_mapping = self._load_stock_board_mapping()

        if self.stock_board_mapping is None or self.stock_board_mapping.empty:
            return {
                'sectors': [],
                'is_hot_sector': False,
                'is_mainline_sector': False,
                'sector_count': 0
            }

        try:
            # 通过股票代码查找（优先）
            if stock_code:
                # 修复pandas Series布尔值歧义问题
                code_mask = self.stock_board_mapping['代码'] == stock_code
                matching_rows = self.stock_board_mapping.loc[code_mask]
                stock_sectors = matching_rows['概念名称'].tolist()
            else:
                # 通过股票名称查找（备用）
                stock_sectors = []

            # 定义热门板块和主线板块
            hot_sectors = [
                "人工智能", "芯片概念", "5G概念", "新能源汽车", "锂电池",
                "光伏概念", "风电", "储能", "半导体", "软件开发",
                "医疗器械", "创新药", "CRO", "军工", "航空航天"
            ]

            mainline_sectors = [
                "人工智能", "芯片概念", "新能源汽车", "锂电池", "光伏概念"
            ]

            # 判断是否属于热门板块或主线板块
            is_hot_sector = any(sector in hot_sectors for sector in stock_sectors)
            is_mainline_sector = any(sector in mainline_sectors for sector in stock_sectors)

            return {
                'sectors': stock_sectors,
                'is_hot_sector': is_hot_sector,
                'is_mainline_sector': is_mainline_sector,
                'sector_count': len(stock_sectors)
            }

        except Exception as e:
            print(f"⚠️ 获取股票{stock_name}板块信息失败: {e}")
            return {
                'sectors': [],
                'is_hot_sector': False,
                'is_mainline_sector': False,
                'sector_count': 0
            }

    def _analyze_sector_leadership(self, stock_name, stock_code, current_market_data, sector_info):
        """分析股票在板块内的龙头地位"""
        if not sector_info['sectors'] or current_market_data is None or current_market_data.empty:
            return {
                'is_sector_leader': False,
                'sector_rank': None,
                'sector_leadership_score': 0.0,
                'leadership_reason': '无板块信息或市场数据'
            }

        try:
            # 获取当前股票的资金流入
            # 修复pandas Series布尔值歧义问题
            name_mask = current_market_data['名称'] == stock_name
            current_stock_data = current_market_data.loc[name_mask]
            if current_stock_data.empty:
                return {
                    'is_sector_leader': False,
                    'sector_rank': None,
                    'sector_leadership_score': 0.0,
                    'leadership_reason': '未找到当前股票数据'
                }

            current_inflow = current_stock_data.iloc[0]['今日主力净流入-净额']

            # 分析每个板块内的排名
            leadership_scores = []
            sector_ranks = []

            for sector in sector_info['sectors'][:3]:  # 只分析前3个主要板块
                sector_leadership = self._calculate_sector_leadership_score(
                    stock_name, stock_code, current_inflow, sector, current_market_data
                )
                leadership_scores.append(sector_leadership['score'])
                sector_ranks.append(sector_leadership['rank'])

            if not leadership_scores:
                return {
                    'is_sector_leader': False,
                    'sector_rank': None,
                    'sector_leadership_score': 0.0,
                    'leadership_reason': '无有效板块数据'
                }

            # 计算综合龙头得分
            max_score = max(leadership_scores)
            best_rank = min(sector_ranks) if sector_ranks else None

            # 龙头判断标准
            is_sector_leader = False
            leadership_reason = ""

            if max_score >= 0.8:  # 绝对龙头
                is_sector_leader = True
                leadership_reason = f"板块绝对龙头(得分{max_score:.2f})"
            elif max_score >= 0.6 and best_rank and best_rank <= 3:  # 前3名且得分较高
                is_sector_leader = True
                leadership_reason = f"板块前排龙头(排名{best_rank}，得分{max_score:.2f})"
            elif best_rank and best_rank == 1:  # 板块第1名
                is_sector_leader = True
                leadership_reason = f"板块资金第1名(得分{max_score:.2f})"
            else:
                leadership_reason = f"非板块龙头(最佳排名{best_rank}，得分{max_score:.2f})"

            return {
                'is_sector_leader': is_sector_leader,
                'sector_rank': best_rank,
                'sector_leadership_score': max_score,
                'leadership_reason': leadership_reason
            }

        except Exception as e:
            return {
                'is_sector_leader': False,
                'sector_rank': None,
                'sector_leadership_score': 0.0,
                'leadership_reason': f'分析失败: {e}'
            }

    def _calculate_sector_leadership_score(self, stock_name, stock_code, current_inflow, sector_name, current_market_data):
        """计算股票在特定板块内的龙头得分"""
        try:
            if self.stock_board_mapping is None:
                return {'score': 0.0, 'rank': None}

            # 获取该板块内的所有股票代码
            sector_stocks = self.stock_board_mapping[
                self.stock_board_mapping['概念名称'] == sector_name
            ]['代码'].tolist()

            if not sector_stocks:
                return {'score': 0.0, 'rank': None}

            # 从市场数据中筛选出板块内股票
            sector_market_data = current_market_data[
                current_market_data['代码'].isin(sector_stocks)
            ].copy()

            if sector_market_data.empty:
                return {'score': 0.0, 'rank': None}

            # 按资金流入排序
            sector_market_data = sector_market_data.sort_values(
                by='今日主力净流入-净额', ascending=False
            )

            # 找到当前股票在板块内的排名
            stock_rank = None
            for i, row in sector_market_data.iterrows():
                if row['名称'] == stock_name:
                    stock_rank = sector_market_data.index.get_loc(i) + 1
                    break

            if stock_rank is None:
                return {'score': 0.0, 'rank': None}

            total_stocks = len(sector_market_data)

            # 计算龙头得分
            if stock_rank == 1:
                # 第1名：检查领先优势
                if total_stocks >= 2:
                    first_inflow = sector_market_data.iloc[0]['今日主力净流入-净额']
                    second_inflow = sector_market_data.iloc[1]['今日主力净流入-净额']
                    if second_inflow > 0:
                        lead_ratio = first_inflow / second_inflow
                        if lead_ratio >= 2.0:
                            score = 1.0  # 绝对龙头
                        elif lead_ratio >= 1.5:
                            score = 0.8  # 明显龙头
                        else:
                            score = 0.6  # 微弱龙头
                    else:
                        score = 0.9  # 唯一正流入
                else:
                    score = 0.7  # 板块内唯一股票
            elif stock_rank <= 3:
                # 前3名
                score = max(0.3, 0.8 - (stock_rank - 1) * 0.2)
            elif stock_rank <= 5:
                # 前5名
                score = max(0.1, 0.4 - (stock_rank - 4) * 0.1)
            else:
                # 其他
                score = max(0.0, 0.2 - (stock_rank - 6) * 0.02)

            return {'score': score, 'rank': stock_rank}

        except Exception as e:
            return {'score': 0.0, 'rank': None}

    def _calculate_sector_factor(self, sector_info, leadership_info):
        """计算板块因子，用于调整检测阈值"""

        # 基础倍数（无调整）
        base_multiplier = 1.0

        # 板块类型加分
        sector_multiplier = base_multiplier
        if sector_info['is_mainline_sector']:
            # 主线板块：大幅降低阈值（更容易触发）
            sector_multiplier = 0.6  # 降低40%
        elif sector_info['is_hot_sector']:
            # 热门板块：适度降低阈值
            sector_multiplier = 0.75  # 降低25%

        # 龙头地位加分
        leadership_multiplier = base_multiplier
        if leadership_info['is_sector_leader']:
            leadership_score = leadership_info['sector_leadership_score']
            if leadership_score >= 0.8:
                # 绝对龙头：大幅降低阈值
                leadership_multiplier = 0.5  # 降低50%
            elif leadership_score >= 0.6:
                # 明显龙头：适度降低阈值
                leadership_multiplier = 0.7  # 降低30%
            else:
                # 一般龙头：小幅降低阈值
                leadership_multiplier = 0.85  # 降低15%

        # 板块数量加分（概念丰富度）
        concept_multiplier = base_multiplier
        if sector_info['sector_count'] >= 5:
            # 概念丰富：小幅降低阈值
            concept_multiplier = 0.9  # 降低10%

        # 综合计算最终倍数
        final_wra_multiplier = sector_multiplier * leadership_multiplier * concept_multiplier
        final_ct_multiplier = sector_multiplier * leadership_multiplier
        final_inflow_multiplier = sector_multiplier * leadership_multiplier

        # 设置合理的下限，避免阈值过低
        final_wra_multiplier = max(0.3, final_wra_multiplier)
        final_ct_multiplier = max(0.4, final_ct_multiplier)
        final_inflow_multiplier = max(0.4, final_inflow_multiplier)

        return {
            'wra_multiplier': final_wra_multiplier,
            'ct_multiplier': final_ct_multiplier,
            'inflow_multiplier': final_inflow_multiplier,
            'sector_info': sector_info,
            'leadership_info': leadership_info,
            'adjustment_reason': self._generate_adjustment_reason(
                sector_info, leadership_info, final_wra_multiplier
            )
        }

    def _generate_adjustment_reason(self, sector_info, leadership_info, final_multiplier):
        """生成阈值调整原因说明"""
        reasons = []

        if sector_info['is_mainline_sector']:
            reasons.append("主线板块")
        elif sector_info['is_hot_sector']:
            reasons.append("热门板块")

        if leadership_info['is_sector_leader']:
            score = leadership_info['sector_leadership_score']
            if score >= 0.8:
                reasons.append("绝对龙头")
            elif score >= 0.6:
                reasons.append("明显龙头")
            else:
                reasons.append("板块龙头")

        if sector_info['sector_count'] >= 5:
            reasons.append("概念丰富")

        if reasons:
            adjustment_pct = (1 - final_multiplier) * 100
            return f"{'+'.join(reasons)}(阈值降低{adjustment_pct:.0f}%)"
        else:
            return "无特殊调整"


def get_log_file_path(base_name, date_str, current_time=None):
    """
    获取日志文件路径，支持按小时分割

    参数:
    - base_name: 基础文件名（如 'analysis_log', 'breakthrough_signals'）
    - date_str: 日期字符串
    - current_time: 当前时间（用于按小时分割）

    返回:
    - 日志文件路径
    """
    # 确保log目录存在
    log_dir = 'log'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 基础文件路径（总体日志文件）
    base_file_path = os.path.join(log_dir, f"{base_name}_{date_str}.txt")

    # 如果不需要按小时分割，直接返回基础路径
    if not SPLIT_LOG_BY_HOUR or current_time is None:
        return base_file_path

    # 按小时分割的文件路径
    if isinstance(current_time, str):
        # 如果是字符串格式的时间，解析为time对象
        try:
            time_obj = datetime.strptime(current_time, '%H:%M:%S').time()
        except:
            return base_file_path
    else:
        time_obj = current_time

    # 获取小时
    hour = time_obj.hour

    # 生成按小时分割的文件名
    hour_file_path = os.path.join(log_dir, f"{base_name}_{date_str}_{hour:02d}-{hour+1:02d}.txt")

    return hour_file_path


def write_to_log_files(content, main_log_path, hourly_log_path=None):
    """
    同时写入总体日志文件和按小时分割的日志文件

    参数:
    - content: 要写入的内容
    - main_log_path: 总体日志文件路径
    - hourly_log_path: 按小时分割的日志文件路径（可选）
    """
    try:
        # 写入总体日志文件
        with open(main_log_path, 'a', encoding='utf-8') as f:
            f.write(content)

        # 如果有按小时分割的日志文件路径，也写入该文件
        if hourly_log_path and SPLIT_LOG_BY_HOUR:
            with open(hourly_log_path, 'a', encoding='utf-8') as f:
                f.write(content)
    except Exception as e:
        print(f"写入日志文件失败: {e}")


class LogCapture:
    """日志捕获类，用于将print输出同时保存到文件，支持动态按小时分割"""
    def __init__(self, log_file_path, date_str=None):
        self.log_file_path = log_file_path
        self.date_str = date_str
        self.original_stdout = sys.stdout
        self.log_file = None
        self.current_hourly_log_file = None
        self.current_hourly_log_path = None

    def __enter__(self):
        # 首次打开时使用覆盖模式清空文件，然后切换到追加模式
        with open(self.log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"=== 资金断层与加速度分析日志 - {self.date_str} ===\n")
            f.write("分析版本：V9.5 - 黄金法则版\n")
            f.write("=" * 60 + "\n\n")

        # 然后以追加模式打开用于后续写入
        self.log_file = open(self.log_file_path, 'a', encoding='utf-8')
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout
        if self.log_file:
            self.log_file.close()
        if self.current_hourly_log_file:
            self.current_hourly_log_file.close()

    def update_hourly_log(self, current_time):
        """根据当前时间更新按小时分割的日志文件"""
        if not SPLIT_LOG_BY_HOUR or not self.date_str or not current_time:
            return

        # 获取当前时间对应的按小时日志文件路径
        new_hourly_log_path = get_log_file_path("analysis_log", self.date_str, current_time)

        # 如果路径发生变化，切换文件
        if new_hourly_log_path != self.current_hourly_log_path:
            # 关闭当前的按小时日志文件
            if self.current_hourly_log_file:
                self.current_hourly_log_file.close()

            # 打开新的按小时日志文件
            self.current_hourly_log_path = new_hourly_log_path
            self.current_hourly_log_file = open(self.current_hourly_log_path, 'a', encoding='utf-8')

    def write(self, text):
        # 同时写入控制台和文件
        self.original_stdout.write(text)
        if self.log_file:
            self.log_file.write(text)
            self.log_file.flush()
        if self.current_hourly_log_file:
            self.current_hourly_log_file.write(text)
            self.current_hourly_log_file.flush()

    def flush(self):
        self.original_stdout.flush()
        if self.log_file:
            self.log_file.flush()
        if self.current_hourly_log_file:
            self.current_hourly_log_file.flush()


def format_amount(amount):
    if amount is None or not isinstance(amount, (int, float)): return 'N/A'
    if abs(amount) >= 1e8: return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def convert_to_float(value):
    if isinstance(value, str):
        try:
            # 处理中文单位
            value = value.replace('%', '').replace(',', '')
            if '亿' in value:
                return float(value.replace('亿', '')) * 1e8
            elif '万' in value:
                return float(value.replace('万', '')) * 1e4
            else:
                return float(value)
        except (ValueError, TypeError):
            return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

def format_money(value):
    """将数字格式化为中文单位显示"""
    try:
        value = float(value)
        if abs(value) >= 1e8:
            return f"{value/1e8:.2f}亿"
        elif abs(value) >= 1e4:
            return f"{value/1e4:.2f}万"
        else:
            return f"{value:.2f}"
    except (ValueError, TypeError):
        return str(value)


def check_historical_max_inflow(stock_data, query_date):
    """
    检查股票是否大于历史最大资金流入

    参数:
    - stock_data: DataFrame，包含股票数据，必须有'名称'、'代码'列，可选'今日主力净流入-净额'、'今日主力净流入-净占比'、'今日超大单净流入-净额'、'今日超大单净流入-净占比'列
    - query_date: str，查询日期，格式'YYYY-MM-DD'

    返回:
    - DataFrame，添加了'大于历史资金流入'列

    说明:
    - 检查主力净流入净额、主力净流入净占比、超大单净流入净额、超大单净流入净占比四个指标
    - 如果任意一个指标超过历史最大值，则标记为'是'
    """
    if fq is None:
        # 如果无法导入查询工具，返回原数据并添加空列
        stock_data['大于历史资金流入'] = ''
        return stock_data

    try:
        # 获取所有股票代码 - 支持多种列名
        stock_codes = []
        code_column = None

        # 查找代码列（支持多种可能的列名）
        possible_code_columns = ['代码', '股票代码', 'code', 'stock_code']
        for col in possible_code_columns:
            if col in stock_data.columns:
                code_column = col
                break

        if code_column is None:
            # 如果没有代码列，尝试通过股票名称查询
            print("警告: 未找到股票代码列，尝试通过股票名称查询历史数据")

            # 查找名称列（支持多种可能的列名）
            name_column = None
            possible_name_columns = ['名称', 'name', '股票名称']
            for col in possible_name_columns:
                if col in stock_data.columns:
                    name_column = col
                    break

            if name_column is not None:
                for _, row in stock_data.iterrows():
                    if pd.notna(row[name_column]):
                        # 通过股票名称查询代码
                        try:
                            stock_info = fq.get_stock_info(row[name_column])
                            if stock_info and 'code' in stock_info:
                                stock_codes.append(str(stock_info['code']))
                            else:
                                stock_codes.append('')  # 添加空代码占位
                        except:
                            stock_codes.append('')  # 查询失败时添加空代码占位
            else:
                print("警告: 未找到股票名称列，历史最大值检查将跳过")
                stock_data['大于历史资金流入'] = ''
                return stock_data
        else:
            # 有代码列的情况
            for _, row in stock_data.iterrows():
                if code_column in row and pd.notna(row[code_column]):
                    stock_codes.append(str(row[code_column]))
                else:
                    stock_codes.append('')  # 添加空代码占位

        # 过滤掉空的股票代码
        valid_stock_codes = [code for code in stock_codes if code and code.strip()]

        if not valid_stock_codes:
            print("警告: 没有有效的股票代码，历史最大值检查将跳过")
            stock_data['大于历史资金流入'] = ''
            return stock_data

        # 查询历史最大值 - 主力净流入净额
        hist_max_amount_df = fq.query_historical_max_value(
            stock_codes=valid_stock_codes,
            query_date=query_date,
            metric='main_net_inflow'
        )

        # 查询历史最大值 - 主力净流入净占比
        hist_max_ratio_df = fq.query_historical_max_value(
            stock_codes=valid_stock_codes,
            query_date=query_date,
            metric='main_net_ratio'
        )

        # 查询历史最大值 - 超大单净流入净额
        hist_max_super_amount_df = fq.query_historical_max_value(
            stock_codes=valid_stock_codes,
            query_date=query_date,
            metric='super_large_net_inflow'
        )

        # 查询历史最大值 - 超大单净流入净占比
        hist_max_super_ratio_df = fq.query_historical_max_value(
            stock_codes=valid_stock_codes,
            query_date=query_date,
            metric='super_large_net_ratio'
        )

        # 创建历史最大值字典
        hist_max_amount_dict = {}
        hist_max_ratio_dict = {}
        hist_max_super_amount_dict = {}
        hist_max_super_ratio_dict = {}

        if not hist_max_amount_df.empty:
            for _, row in hist_max_amount_df.iterrows():
                hist_max_amount_dict[row['stock_code']] = row['historical_max_value']

        if not hist_max_ratio_df.empty:
            for _, row in hist_max_ratio_df.iterrows():
                hist_max_ratio_dict[row['stock_code']] = row['historical_max_value']

        if not hist_max_super_amount_df.empty:
            for _, row in hist_max_super_amount_df.iterrows():
                hist_max_super_amount_dict[row['stock_code']] = row['historical_max_value']

        if not hist_max_super_ratio_df.empty:
            for _, row in hist_max_super_ratio_df.iterrows():
                hist_max_super_ratio_dict[row['stock_code']] = row['historical_max_value']

        # 定义前N天检查函数 - 找出具体能超过前几天
        def check_recent_days_max(row, stock_code, query_date, max_days=100):
            """检查能超过前几天的最大值，返回具体天数"""
            try:
                current_amount = safe_float_convert(row.get('今日主力净流入-净额', 0))
                current_ratio = safe_float_convert(row.get('今日主力净流入-净占比', 0))
                current_super_amount = safe_float_convert(row.get('今日超大单净流入-净额', 0))
                current_super_ratio = safe_float_convert(row.get('今日超大单净流入-净占比', 0))

                # 准备当前值字典
                current_values = {stock_code: current_amount}
                current_ratio_values = {stock_code: current_ratio}
                current_super_amount_values = {stock_code: current_super_amount}
                current_super_ratio_values = {stock_code: current_super_ratio}

                # 查询各指标能超过的最大天数
                max_exceeded_days = 0

                # 检查主力净流入净额
                if pd.notna(current_amount) and current_amount != 0:
                    amount_result = fq.find_exceeded_days_count(
                        stock_codes=[stock_code],
                        query_date=query_date,
                        current_values=current_values,
                        metric='main_net_inflow',
                        max_days=max_days
                    )
                    if not amount_result.empty:
                        exceeded_days = amount_result.iloc[0]['exceeded_days']
                        max_exceeded_days = max(max_exceeded_days, exceeded_days)

                # 检查主力净流入净占比
                if pd.notna(current_ratio) and current_ratio != 0:
                    ratio_result = fq.find_exceeded_days_count(
                        stock_codes=[stock_code],
                        query_date=query_date,
                        current_values=current_ratio_values,
                        metric='main_net_ratio',
                        max_days=max_days
                    )
                    if not ratio_result.empty:
                        exceeded_days = ratio_result.iloc[0]['exceeded_days']
                        max_exceeded_days = max(max_exceeded_days, exceeded_days)

                # 检查超大单净流入净额
                if pd.notna(current_super_amount) and current_super_amount != 0:
                    super_amount_result = fq.find_exceeded_days_count(
                        stock_codes=[stock_code],
                        query_date=query_date,
                        current_values=current_super_amount_values,
                        metric='super_large_net_inflow',
                        max_days=max_days
                    )
                    if not super_amount_result.empty:
                        exceeded_days = super_amount_result.iloc[0]['exceeded_days']
                        max_exceeded_days = max(max_exceeded_days, exceeded_days)

                # 检查超大单净流入净占比
                if pd.notna(current_super_ratio) and current_super_ratio != 0:
                    super_ratio_result = fq.find_exceeded_days_count(
                        stock_codes=[stock_code],
                        query_date=query_date,
                        current_values=current_super_ratio_values,
                        metric='super_large_net_ratio',
                        max_days=max_days
                    )
                    if not super_ratio_result.empty:
                        exceeded_days = super_ratio_result.iloc[0]['exceeded_days']
                        max_exceeded_days = max(max_exceeded_days, exceeded_days)

                # 返回结果
                if max_exceeded_days >= 3:  # 至少要超过前3天才显示
                    return f'前{max_exceeded_days}天'
                else:
                    return ''

            except Exception as e:
                return ''

        # 数据类型转换函数
        def safe_float_convert(value):
            """安全地将值转换为浮点数"""
            if pd.isna(value):
                return 0.0
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                # 移除可能的单位和符号
                value = value.replace('亿', '').replace('万', '').replace('%', '').replace(',', '').strip()
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0
            return 0.0

        # 检查每只股票是否大于历史最大值
        def check_stock_historical_max(row):
            # 处理股票代码，确保格式正确
            if code_column is not None:
                raw_code = row.get(code_column, '')
                if pd.isna(raw_code) or raw_code == '':
                    stock_code = ''
                else:
                    # 如果是浮点数，先转换为整数再转换为字符串
                    if isinstance(raw_code, float):
                        stock_code = str(int(raw_code))
                    else:
                        stock_code = str(raw_code)
            else:
                # 没有代码列时，从stock_codes列表中获取对应的代码
                row_index = row.name  # 获取行索引
                if row_index < len(stock_codes):
                    stock_code = stock_codes[row_index]
                else:
                    stock_code = ''
            current_amount = safe_float_convert(row.get('今日主力净流入-净额', 0))
            current_ratio = safe_float_convert(row.get('今日主力净流入-净占比', 0))
            current_super_amount = safe_float_convert(row.get('今日超大单净流入-净额', 0))
            current_super_ratio = safe_float_convert(row.get('今日超大单净流入-净占比', 0))

            # 获取历史最大值
            hist_max_amount = hist_max_amount_dict.get(stock_code)
            hist_max_ratio = hist_max_ratio_dict.get(stock_code)
            hist_max_super_amount = hist_max_super_amount_dict.get(stock_code)
            hist_max_super_ratio = hist_max_super_ratio_dict.get(stock_code)

            # 检查是否大于历史最大值
            amount_exceeds = False
            ratio_exceeds = False
            super_amount_exceeds = False
            super_ratio_exceeds = False

            if hist_max_amount is not None and pd.notna(hist_max_amount) and pd.notna(current_amount):
                amount_exceeds = current_amount > hist_max_amount

            if hist_max_ratio is not None and pd.notna(hist_max_ratio) and pd.notna(current_ratio):
                ratio_exceeds = current_ratio > hist_max_ratio

            if hist_max_super_amount is not None and pd.notna(hist_max_super_amount) and pd.notna(current_super_amount):
                super_amount_exceeds = current_super_amount > hist_max_super_amount

            if hist_max_super_ratio is not None and pd.notna(hist_max_super_ratio) and pd.notna(current_super_ratio):
                super_ratio_exceeds = current_super_ratio > hist_max_super_ratio

            # 如果任意一个指标大于历史最大值，则标记为是
            if amount_exceeds or ratio_exceeds or super_amount_exceeds or super_ratio_exceeds:
                return '是'
            else:
                # 如果不大于历史最大值，检查是否大于前N天
                return check_recent_days_max(row, stock_code, query_date)

        stock_data = stock_data.copy()  # 避免SettingWithCopyWarning
        stock_data['大于历史资金流入'] = stock_data.apply(check_stock_historical_max, axis=1)

    except Exception as e:
        print(f"检查历史最大值时发生错误: {e}")
        import traceback
        traceback.print_exc()
        stock_data['大于历史资金流入'] = ''

    return stock_data


def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳，支持多种格式"""
    # 格式1: HH-MM_ (旧格式) 例如: 09-30_zt_pool.csv, 09-30_ths_big_deal.csv
    match = re.search(r'^(\d{2})-(\d{2})_', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    # 格式2: _YYYYMMDD_HHMMSS (新格式) 例如: fund_flow_rank_20250725_093047.csv
    match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式3: _HHMMSS.csv (新格式) 例如: 涨停股池_akshare_东方财富_093651.csv
    match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式4: limit_up_pool_YYYYMMDD_HHMMSS.csv 例如: limit_up_pool_20250728_093026.csv
    match = re.search(r'limit_up_pool_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式4a: zt_pool_YYYYMMDD_HHMMSS.csv 例如: zt_pool_20250730_093223.csv（新增支持）
    match = re.search(r'zt_pool_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式5: 股票资金流_zssz/zssh_YYYYMMDD_HHMMSS.csv 例如: 股票资金流_zssz_20250728_093050.csv
    match = re.search(r'股票资金流_zs[sh|sz]{2}_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式6: 实时概念资金流_YYYYMMDD_HHMMSS.csv 例如: 实时概念资金流_20250728_093453.csv
    match = re.search(r'实时[概念|板块]资金流_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式7: HH-MM_文件名.csv 例如: 09-34_fund_flow_tpdog.csv, 09-34_concept_fund_flow_tpdog.csv
    match = re.search(r'^(\d{2})-(\d{2})_.*\.csv$', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    return None


def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式"""
    # 大单买盘文件匹配
    big_deal_patterns = [
        'ths_big_deal.csv',       # 同花顺大单买盘
        'movers_大笔买入.csv',     # 大笔买入异动
        'big_deal_',              # 大单买盘通用
        'movers_有大买盘'          # 有大买盘异动
    ]

    # 板块异动文件匹配
    board_changes_patterns = [
        'board_changes.csv',      # 板块异动
        'sector_changes.csv',     # 板块变化
        'concept_changes.csv'     # 概念异动
    ]

    # 涨停股池文件匹配
    limit_up_patterns = [
        'limit_up_pool_',         # 涨停股池
        'zt_pool_',              # 涨停池（新增：支持zt_pool_YYYYMMDD_HHMMSS.csv格式）
        'zt_pool.csv',           # 涨停池
        '涨停股池_',              # 涨停股池中文
        'limit_up_'              # 涨停相关
    ]

    # 创月新高指标文件匹配
    new_high_patterns = [
        'indicator_创月新高.csv',  # 创月新高指标
        'new_high_',             # 新高指标
        'monthly_high_',         # 月新高
        'indicator_'             # 指标文件通用
    ]

    # 个股资金流文件匹配（扩展版）
    stock_flow_patterns = [
        'fund_flow_rank_',        # 个股资金流排名
        'fund_flow_tpdog.csv',    # tpdog个股资金流
        'ths_fund_flow.csv',      # 同花顺个股资金流
        'fund_flow_akshare.csv',  # akshare个股资金流
        'individual_fund_flow_',  # 个股资金流排名
        '股票资金流_zssz_',        # 深圳个股资金流
        '股票资金流_zssh_',        # 上海个股资金流
        'stock_fund_flow_'        # 个股资金流通用
    ]

    # 概念资金流文件匹配（扩展版）
    concept_patterns = [
        'concept_fund_flow_tpdog.csv',
        'concept_fund_flow_akshare.csv',
        'concept_fund_flow_',
        '实时概念资金流_',         # 实时概念资金流
        'realtime_concept_flow_'  # 实时概念流
    ]

    # 行业资金流文件匹配（扩展版）
    sector_patterns = [
        'sector_fund_flow_tpdog.csv',
        'sector_fund_flow_akshare.csv',
        'sector_fund_flow_rank_',
        '实时板块资金流_',         # 实时板块资金流
        'realtime_sector_flow_'   # 实时板块流
    ]

    # 按长度降序排序，长模式优先匹配
    all_patterns = []
    for pattern in big_deal_patterns:
        all_patterns.append((pattern, 'big_deal'))
    for pattern in board_changes_patterns:
        all_patterns.append((pattern, 'board_changes'))
    for pattern in limit_up_patterns:
        all_patterns.append((pattern, 'limit_up'))
    for pattern in new_high_patterns:
        all_patterns.append((pattern, 'new_high'))
    for pattern in stock_flow_patterns:
        all_patterns.append((pattern, 'stock_flow'))
    for pattern in concept_patterns:
        all_patterns.append((pattern, 'concept'))
    for pattern in sector_patterns:
        all_patterns.append((pattern, 'sector'))

    all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

    # 逐一匹配模式
    for pattern, file_type in all_patterns:
        if pattern in filename:
            return file_type

    return 'other'


def find_latest_file(file_list, current_time):
    """
    查找最新的文件，确保遵循回测时间限制，不读取未来数据
    """
    relevant_file = None
    best_timestamp = None

    # 处理所有文件，不要早退出
    for f in sorted(file_list):
        try:
            timestamp = extract_timestamp_from_filename(f)
            if not timestamp: continue
            f_ts = datetime.strptime(timestamp, '%H%M%S').time()

            # 核心回测时间验证：确保不读取未来数据
            if f_ts <= current_time:
                # 如果时间戳更晚，或者时间戳相同但文件名更具体（包含日期），则优先选择
                if (best_timestamp is None or
                    f_ts > best_timestamp or
                    (f_ts == best_timestamp and BACKTEST_DATE.replace('-', '') in f and
                     (relevant_file is None or BACKTEST_DATE.replace('-', '') not in relevant_file))):
                    relevant_file = f
                    best_timestamp = f_ts
        except (ValueError, IndexError):
            continue
    return relevant_file


def analyze_market_competition(inflows, names):
    """市场竞争激烈度分析器：检测是否存在明显龙头（完整版）"""
    
    if len(inflows) < 3:
        return {"is_competitive": False, "competition_type": "insufficient_data"}
    
    # 计算前几名的相对差距
    top5_ratios = []
    for i in range(min(5, len(inflows)-1)):
        if inflows[i+1] > 0:
            ratio = inflows[i] / inflows[i+1]
            top5_ratios.append(ratio)
    
    if not top5_ratios:
        return {"is_competitive": False, "competition_type": "no_data"}
    
    # 分析前几名的竞争情况
    top2_ratios = top5_ratios[:2] if len(top5_ratios) >= 2 else top5_ratios
    top3_ratios = top5_ratios[:3] if len(top5_ratios) >= 3 else top5_ratios
    
    # 各种竞争格局的检测
    is_highly_competitive = False
    competition_type = "clear_leader"
    leading_group = names[:1]
    reference_ratio = 0
    
    # 【优先检测】单龙头明显领先：如果第1名相比第2名有足够优势，直接返回非竞争
    if (len(top5_ratios) >= 1 and 
        top5_ratios[0] >= 1.12):  # 第1名领先≥12%，视为明显优势
        return {"is_competitive": False, "competition_type": "clear_leader"}
    
    # 1. 三足鼎立：前3名都非常接近
    if (len(top3_ratios) >= 2 and 
        all(r < 1.12 for r in top3_ratios[:2]) and  # 前3名都很接近
        max(top3_ratios[:2]) < 1.12):  # 最大差距也不大
        is_highly_competitive = True
        competition_type = "三足鼎立"
        leading_group = names[:3]
        reference_ratio = max(top3_ratios[:2])
    
    # 2. 双强争霸：前2名非常接近，但第3名有一定差距
    elif (len(top5_ratios) >= 1 and 
          top5_ratios[0] < 1.08 and  # 第1/第2名非常接近
          (len(top5_ratios) < 2 or top5_ratios[1] > 1.15)):  # 第3名有明显差距(降低阈值从1.20到1.15)
        is_highly_competitive = True
        competition_type = "双强争霸"
        leading_group = names[:2]
        reference_ratio = top5_ratios[0]
    
    # 3. 四强竞争：前4名都在同一水平线上（新增）
    elif (len(top5_ratios) >= 3 and
          all(r < 1.25 for r in top5_ratios[:3]) and  # 前4名差距都不太大
          sum(1 for r in top5_ratios[:3] if r < 1.15) >= 2):  # 至少2个位置差距很小
        is_highly_competitive = True
        competition_type = "多强竞争"
        leading_group = names[:4]
        reference_ratio = max(top5_ratios[:3])
    
    # 4. 竞争激烈：前2-3名比较接近
    elif (len(top2_ratios) > 0 and
          max(top2_ratios) < 1.15 and 
          (len(top2_ratios) < 2 or sum(top2_ratios)/len(top2_ratios) < 1.12)):
        is_highly_competitive = True
        competition_type = "竞争激烈"
        leading_group = names[:3]
        reference_ratio = max(top2_ratios) if top2_ratios else 0
    
    if is_highly_competitive:
        return {
            "is_competitive": True,
            "competition_type": competition_type,
            "leading_group": leading_group,
            "max_ratio": reference_ratio,
            "avg_ratio": sum(top2_ratios)/len(top2_ratios) if top2_ratios else reference_ratio
        }
    
    return {"is_competitive": False, "competition_type": "clear_leader"}


def analyze_market_state(inflows):
    inflows_yi = [x / 1e8 for x in inflows[:5]]
    total_top5 = sum(inflows_yi)
    
    # 资金规模分级
    if total_top5 > 80: scale = "超大资金"
    elif total_top5 > 40: scale = "大资金" 
    elif total_top5 > 15: scale = "中等资金"
    else: scale = "小资金"
    
    # 集中度计算 (前2名占前5名比例)
    concentration = sum(inflows_yi[:2]) / total_top5 if total_top5 > 0 else 0
    
    # 分散度计算 (变异系数)
    dispersion = np.std(inflows_yi) / np.mean(inflows_yi) if np.mean(inflows_yi) > 0 else 0
    
    return {
        "scale": scale,
        "concentration": concentration,
        "dispersion": dispersion,
        "total_top5": total_top5,
        "avg_top5": np.mean(inflows_yi)
    }


def find_all_gap_points(inflows):
    """全局断层点扫描器：找到所有可能的断层点（智能优先版）"""
    gap_scores = []
    
    for i in range(min(6, len(inflows)-1)):  # 只检查前6个位置
        abs_gap = inflows[i] - inflows[i+1]  # 绝对差距(元)
        rel_gap = inflows[i] / inflows[i+1] if inflows[i+1] > 0 else float('inf')  # 相对差距
        
        # 位置权重：前面的断层更重要
        if i == 0:  # 第1名后
            position_weight = 1.0
        elif i == 1:  # 第2名后
            position_weight = 0.67
        else:
            position_weight = 1.0 / (i/2 + 1)
        
        # 双龙头特殊处理：只有当前2名真正接近时才加权
        if i == 1 and len(inflows) >= 3:
            first_second_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
            # 严格限制：只有相差<1.15倍才认为是真正的双龙头
            if first_second_ratio < 1.15:
                dual_leader_bonus = 2.0  # 提高加权倍数，但只对真正的双龙头生效
                position_weight *= dual_leader_bonus
        
        # 综合断层得分 = 绝对差距(亿) × 相对差距 × 位置权重
        gap_score = (abs_gap / 1e8) * rel_gap * position_weight
        
        gap_scores.append({
            "position": i,
            "abs_gap": abs_gap,
            "rel_gap": rel_gap,
            "score": gap_score,
            "position_weight": position_weight
        })
    
    # 智能断层点选择：优先考虑单龙头的可能性
    max_gap = max(gap_scores, key=lambda x: x["score"])
    
    # 单龙头优先检测：如果第1名后有明显优势，且第1名确实领先，优先选择第1名后
    if (len(gap_scores) >= 2 and 
        gap_scores[0]["rel_gap"] >= 1.15 and  # 第1名有明显领先
        gap_scores[0]["abs_gap"] >= 2e8 and   # 绝对差距也不小(≥2亿)
        gap_scores[0]["score"] >= max_gap["score"] * 0.65):  # 得分不能太低(≥最高分的65%)
        
        # 选择第1名后作为主要断层点
        max_gap = gap_scores[0]
    
    return gap_scores, max_gap


def identify_leading_group(inflows, gap_position, names):
    """集团识别器：识别领先集团并分析其特征（优化版）"""
    import numpy as np
    
    # 断层点前为领先集团
    leading_group = inflows[:gap_position+1]
    leading_names = names[:gap_position+1]
    following_group = inflows[gap_position+1:] if gap_position+1 < len(inflows) else []
    
    # 集团内部紧密度验证 - 针对双龙头优化
    if len(leading_group) > 1:
        internal_gaps = [leading_group[i]/leading_group[i+1] 
                        for i in range(len(leading_group)-1)]
        max_internal_gap = max(internal_gaps) if internal_gaps else 1.0
        
        # 双龙头特殊处理：如果集团只有2个成员且都是大额资金，放宽紧密度要求
        if len(leading_group) == 2:
            avg_amount = np.mean(leading_group) / 1e8  # 转换为亿元
            # 大资金双龙头（平均>10亿）：紧密度要求从1.4放宽到1.8
            if avg_amount > 10:
                cohesion_factor = 0.7  # 降低紧密度的惩罚力度
            else:
                cohesion_factor = 1.0
        else:
            cohesion_factor = 1.0
    else:
        max_internal_gap = 1.0
        cohesion_factor = 1.0
    
    return {
        "size": len(leading_group),
        "members": leading_names,
        "amounts": [x/1e8 for x in leading_group],  # 转换为亿元
        "avg_amount": np.mean(leading_group) / 1e8,
        "internal_cohesion": max_internal_gap,
        "cohesion_factor": cohesion_factor,  # 新增：用于调整紧密度评判标准
        "external_gap": leading_group[-1] / following_group[0] if following_group else float('inf')
    }


def calculate_dynamic_thresholds(market_state):
    """动态阈值计算器：根据市场状态自适应调整标准"""
    
    # 基础相对阈值根据资金规模调整
    scale_factors = {
        "超大资金": 1.15,  # 大资金市场门槛更低
        "大资金": 1.25,
        "中等资金": 1.35,
        "小资金": 1.45     # 小资金市场门槛更高
    }
    
    # 根据集中度微调：集中度越高，越容易形成断层
    concentration_adjustment = (1 - market_state["concentration"]) * 0.1
    
    dynamic_relative_threshold = scale_factors[market_state["scale"]] + concentration_adjustment
    
    # 绝对差距阈值：平均值的15-25%
    min_abs_gap_ratio = 0.15 + market_state["dispersion"] * 0.1
    dynamic_absolute_threshold = market_state["avg_top5"] * min_abs_gap_ratio
    
    return {
        "min_relative_gap": dynamic_relative_threshold,
        "min_absolute_gap": dynamic_absolute_threshold,
        "group_cohesion_limit": 1.4,  # 集团内部最大允许差距
        "min_gap_score": 1.5  # 降低最小综合得分要求 (从2.0降到1.5)
    }


def comprehensive_evaluation(market_state, max_gap, group_info, thresholds):
    """综合评判器：多维度智能评分（超大资金优化版）"""
    
    scores = {}
    
    # 1. 相对优势得分 (0-2分)
    rel_score = min(max_gap["rel_gap"] / thresholds["min_relative_gap"], 2.0)
    scores["relative"] = rel_score
    
    # 2. 绝对优势得分 (0-2分) - 针对超大资金市场优化
    abs_score = min((max_gap["abs_gap"]/1e8) / thresholds["min_absolute_gap"], 2.0)
    
    # 超大资金市场特殊处理：如果是明显的单龙头领先，给予绝对优势加分
    if (market_state["scale"] == "超大资金" and 
        max_gap["position"] == 0 and  # 第1名后断层
        max_gap["abs_gap"] >= 3e8 and  # 绝对差距≥3亿
        max_gap["rel_gap"] >= 1.15):   # 相对差距≥15%
        abs_score = min(abs_score * 1.8, 2.0)  # 绝对优势得分提升80%
    
    scores["absolute"] = abs_score
    
    # 3. 集团紧密度得分 (0-2分)：内部越紧密得分越高（优化版）
    effective_cohesion_limit = thresholds["group_cohesion_limit"] / group_info.get("cohesion_factor", 1.0)
    if group_info["internal_cohesion"] <= effective_cohesion_limit:
        cohesion_score = 2.0 - (group_info["internal_cohesion"] - 1.0) / (effective_cohesion_limit - 1.0)
    else:
        cohesion_score = 0.0
    scores["cohesion"] = max(cohesion_score, 0)
    
    # 4. 市场格局得分 (0-2分) - 针对双龙头优化
    concentration = market_state["concentration"]
    if group_info["size"] == 2 and concentration > 0.50:  # 双龙头且中等集中度以上
        pattern_score = 1.8  # 提高双龙头格局的得分
    elif concentration > 0.65:  # 高集中度
        pattern_score = 1.8
    elif concentration > 0.45:  # 中等集中度
        pattern_score = 1.2
    else:  # 分散市场
        pattern_score = 0.6
    scores["pattern"] = pattern_score
    
    # 5. 位置重要性得分 (0-2分)：越靠前的断层越重要
    position_score = 2.0 * max_gap["position_weight"] / 1.2  # 标准化权重
    scores["position"] = min(position_score, 2.0)
    
    # 6. 双龙头特殊加分 (0-0.5分)：识别双龙头格局的特殊奖励
    dual_leader_bonus = 0.0
    if (group_info["size"] == 2 and 
        max_gap["position"] == 1 and  # 第2名后断层
        group_info["avg_amount"] > 10):  # 大资金市场
        dual_leader_bonus = 0.3
    
    # 7. 单龙头特殊加分 (0-0.4分)：超大资金市场中的单龙头奖励（新增）
    single_leader_bonus = 0.0
    if (market_state["scale"] == "超大资金" and
        group_info["size"] == 1 and  # 单龙头
        max_gap["position"] == 0 and  # 第1名后断层
        max_gap["rel_gap"] >= 1.15):  # 有明显领先
        single_leader_bonus = 0.3
    
    scores["dual_leader_bonus"] = dual_leader_bonus
    scores["single_leader_bonus"] = single_leader_bonus
    
    # 综合得分 (加权平均 + 特殊加分)
    weights = {"relative": 0.22, "absolute": 0.22, "cohesion": 0.18, "pattern": 0.18, "position": 0.20}
    base_score = sum(scores[key] * weights[key] for key in weights)
    total_score = base_score + dual_leader_bonus + single_leader_bonus
    
    return total_score, scores


def find_sector_summary_file(sector_name, current_time, data_dir):
    """查找指定板块在指定时间的sector_summary文件"""
    try:
        all_files = os.listdir(data_dir)

        # 查找匹配的sector_summary文件
        sector_files = []
        for f in all_files:
            if f.startswith(f'sector_summary_{sector_name}_') and f.endswith('.csv'):
                sector_files.append(f)

        if not sector_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(sector_files, current_time)

    except Exception as e:
        print(f"查找sector_summary文件失败: {e}")
        return None


def find_concept_summary_file(concept_name, current_time, data_dir):
    """查找指定概念在指定时间的concept_summary文件"""
    try:
        all_files = os.listdir(data_dir)

        # 查找匹配的concept_summary文件
        concept_files = []
        for f in all_files:
            if f.startswith(f'concept_summary_{concept_name}_') and f.endswith('.csv'):
                concept_files.append(f)

        if not concept_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(concept_files, current_time)

    except Exception as e:
        print(f"查找concept_summary文件失败: {e}")
        return None


def find_concept_summary_file(concept_name, current_time, data_dir):
    """查找指定概念在指定时间的concept_summary文件"""
    try:
        all_files = os.listdir(data_dir)

        # 查找匹配的concept_summary文件
        concept_files = []
        for f in all_files:
            if f.startswith(f'concept_summary_{concept_name}_') and f.endswith('.csv'):
                concept_files.append(f)

        if not concept_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(concept_files, current_time)

    except Exception as e:
        print(f"查找concept_summary文件失败: {e}")
        return None


def parse_sector_internal_data(file_path):
    """解析sector_summary文件，返回个股资金流数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 检查必要的列是否存在
        required_cols = ['名称', '今日主力净流入-净额']
        if not all(col in df.columns for col in required_cols):
            print(f"sector_summary文件缺少必要列: {list(df.columns)}")
            return None

        # 转换数据类型
        df['今日主力净流入-净额'] = df['今日主力净流入-净额'].apply(convert_to_float)

        # 过滤正流入数据并排序
        positive_df = df[df['今日主力净流入-净额'] > 0].copy()
        positive_df = positive_df.sort_values(by='今日主力净流入-净额', ascending=False)

        return positive_df

    except Exception as e:
        print(f"解析sector_summary文件失败: {e}")
        return None


def get_stock_flow_file_format(file_path):
    """识别个股资金流文件的具体格式类型"""
    filename = os.path.basename(file_path)
    
    # 基于文件名模式识别格式，按原有的 stock_flow_patterns 完整覆盖
    if 'ths_fund_flow' in filename:
        return 'ths_format'  # 同花顺格式：使用流入流出资金
    elif '股票资金流_zssz_' in filename or '股票资金流_zssh_' in filename:
        return 'exchange_format'  # 交易所格式：深圳/上海
    elif 'fund_flow_rank_' in filename:
        return 'rank_format'  # 排名格式
    elif 'individual_fund_flow_' in filename:
        return 'individual_format'  # 个股资金流排名格式
    elif 'fund_flow_tpdog' in filename:
        return 'tpdog_format'  # tpdog格式
    elif 'fund_flow_akshare' in filename:
        return 'akshare_format'  # akshare格式
    elif 'stock_fund_flow_' in filename:
        return 'stock_flow_format'  # 个股资金流通用格式
    else:
        return 'standard_format'  # 标准格式


def parse_ths_format(df):
    """处理同花顺格式：使用流入资金-流出资金计算净流入"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '流入资金': ['流入资金', 'inflow', '今日主力净流入-总流入'],
        '流出资金': ['流出资金', 'outflow', '今日主力净流入-总流出'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    if df_processed is None:
        return None
    
    # 如果有现成的净流入数据，直接使用
    if '今日主力净流入-净额' in df_processed.columns:
        print(f"同花顺格式使用现成净流入数据，数据形状: {df_processed.shape}")
        return df_processed
    
    # 必须有流入和流出资金才能处理
    if '流入资金' not in df_processed.columns or '流出资金' not in df_processed.columns:
        print("同花顺格式缺少流入资金或流出资金列")
        return None
    
    # 计算主力净流入
    df_processed['今日主力净流入-净额'] = df_processed['流入资金'] - df_processed['流出资金']
    
    print(f"同花顺格式处理完成，数据形状: {df_processed.shape}")
    return df_processed


def parse_exchange_format(df):
    """处理交易所格式：深圳/上海个股资金流"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码'],
        '最新价': ['最新价', 'price', '现价'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'main_net_inflow_pct', '主力净流入占比'],
        '今日超大单净流入-净额': ['今日超大单净流入-净额', 'super_large_net_inflow', '超大单净流入'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    print(f"交易所格式处理完成，数据形状: {df_processed.shape if df_processed is not None else 'None'}")
    return df_processed


def parse_standard_format(df):
    """处理标准格式：包含完整的净流入数据"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'main_net_inflow_pct', '主力净流入占比'],
        '今日超大单净流入-净额': ['今日超大单净流入-净额', 'super_large_net_inflow', '超大单净流入'],
        '今日超大单净流入-净占比': ['今日超大单净流入-净占比', 'super_large_net_inflow_pct', '超大单净流入占比'],
        '今日大单净流入-净额': ['今日大单净流入-净额', 'large_net_inflow', '大单净流入'],
        '今日大单净流入-净占比': ['今日大单净流入-净占比', 'large_net_inflow_pct', '大单净流入占比'],
        '今日中单净流入-净额': ['今日中单净流入-净额', 'medium_net_inflow', '中单净流入'],
        '今日中单净流入-净占比': ['今日中单净流入-净占比', 'medium_net_inflow_pct', '中单净流入占比'],
        '今日小单净流入-净额': ['今日小单净流入-净额', 'small_net_inflow', '小单净流入'],
        '今日小单净流入-净占比': ['今日小单净流入-净占比', 'small_net_inflow_pct', '小单净流入占比'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    print(f"标准格式处理完成，数据形状: {df_processed.shape if df_processed is not None else 'None'}")
    return df_processed


def apply_column_mapping(df, column_mapping):
    """应用列名映射并验证必要列"""
    mapped_columns = {}
    available_columns = []
    
    for target_col, possible_names in column_mapping.items():
        for possible_name in possible_names:
            if possible_name in df.columns:
                mapped_columns[possible_name] = target_col
                available_columns.append(target_col)
                break
    
    # 检查必要列
    if '名称' not in available_columns:
        print(f"缺少股票名称列，可用列: {list(df.columns)}")
        return None
    
    # 重命名列并选择可用列
    df_clean = df.rename(columns=mapped_columns)
    df_clean = df_clean[available_columns].copy()
    
    # 转换数值列
    numeric_columns = [col for col in available_columns if col != '名称']
    for col in numeric_columns:
        df_clean[col] = df_clean[col].apply(convert_to_float)
    
    return df_clean


def parse_stock_flow_data(file_path, file_type):
    """解析个股资金流数据（V8.0 重构版：支持多种文件格式的专门处理）"""
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', nrows=2000)
        if df.empty:
            print(f"文件为空: {file_path}")
            return None
        
        # 识别文件格式
        file_format = get_stock_flow_file_format(file_path)
        print(f"检测到文件格式: {file_format} - {os.path.basename(file_path)}")
        
        # 根据格式选择对应的处理函数
        format_processors = {
            'ths_format': parse_ths_format,
            'exchange_format': parse_exchange_format,
            'rank_format': parse_standard_format,
            'individual_format': parse_standard_format,
            'tpdog_format': parse_standard_format,
            'akshare_format': parse_standard_format,
            'stock_flow_format': parse_standard_format,
            'standard_format': parse_standard_format
        }
        
        processor = format_processors.get(file_format, parse_standard_format)
        df_processed = processor(df)
        
        if df_processed is None:
            print(f"文件处理失败: {file_path}")
            return None
        
        # 验证最终必要列
        if '今日主力净流入-净额' not in df_processed.columns:
            print(f"处理后仍缺少主力净流入数据: {file_path}")
            return None
        
        # 按净流入排序
        df_processed = df_processed.sort_values(by='今日主力净流入-净额', ascending=False)
        df_processed = df_processed.reset_index(drop=True)
        
        print(f"文件处理成功: {len(df_processed)} 只股票")
        return df_processed
        
    except Exception as e:
        print(f"解析个股资金流文件失败 {file_path}: {e}")
        return None


def get_stock_filter_strategy(file_format):
    """根据文件格式返回对应的过滤策略"""
    filter_strategies = {
        'ths_format': 'positive_only',        # 同花顺：只要净流入>0
        'exchange_format': 'positive_only',   # 交易所：只要净流入>0
        'rank_format': 'positive_and_gain',   # 排名格式：净流入>0且涨跌幅>=0
        'individual_format': 'positive_only', # 个股资金流排名：只要净流入>0
        'tpdog_format': 'positive_only',      # tpdog：只要净流入>0
        'akshare_format': 'positive_only',    # akshare：只要净流入>0
        'stock_flow_format': 'positive_only', # 个股资金流通用：只要净流入>0
        'standard_format': 'positive_only'    # 标准：只要净流入>0
    }
    return filter_strategies.get(file_format, 'positive_only')


def apply_stock_filter(stock_flow_data, file_format):
    """根据文件格式应用对应的过滤策略"""
    strategy = get_stock_filter_strategy(file_format)
    
    if strategy == 'positive_and_gain':
        # 要求净流入>0且涨跌幅>=0
        if '今日涨跌幅' in stock_flow_data.columns:
            return stock_flow_data[
                (stock_flow_data['今日主力净流入-净额'] > 0) & 
                (stock_flow_data['今日涨跌幅'] >= 0)
            ]
        else:
            # 如果没有涨跌幅数据，退化为只要求净流入>0
            return stock_flow_data[stock_flow_data['今日主力净流入-净额'] > 0]
    else:
        # 默认策略：只要求净流入>0
        return stock_flow_data[stock_flow_data['今日主力净流入-净额'] > 0]

class ObservationPool:
    """V5.1 持续性验证观察池 - 过滤毛刺信号"""
    
    def __init__(self, observation_minutes=4):
        self.observation_minutes = observation_minutes
        self.pool = {}  # {stock_name: observation_data}
    
    def add_candidate(self, stock_name, timestamp, wra, ct, pf, current_rank, dynamic_thresholds):
        """添加候选股票到观察池"""
        if stock_name not in self.pool:
            self.pool[stock_name] = {
                'first_trigger_time': timestamp,
                'observations': [],
                'confirmed': False
            }
        
        # 添加观察记录
        self.pool[stock_name]['observations'].append({
            'timestamp': timestamp,
            'wra': wra,
            'ct': ct,
            'pf': pf,
            'rank': current_rank,
            'wra_threshold': dynamic_thresholds['dynamic_wra_threshold'] * WRA_MULTIPLIER,
            'ct_threshold': dynamic_thresholds['dynamic_ct_threshold'] * CT_MULTIPLIER
        })

    def check_persistence(self, stock_name, current_time):
        """检查股票的持续性，返回是否确认为有效信号"""
        if stock_name not in self.pool:
            return False

        observation_data = self.pool[stock_name]

        # V5.2 修复：如果信号已经确认过，则不再重复触发
        if observation_data.get('confirmed', False):
            return False

        # 检查观察期是否足够
        from datetime import datetime, date, timedelta
        if isinstance(current_time, datetime):
            current_dt = current_time
        else:
            current_dt = datetime.combine(date.today(), current_time)

        first_trigger_time = observation_data['first_trigger_time']
        if isinstance(first_trigger_time, datetime):
            first_trigger_dt = first_trigger_time
        else:
            first_trigger_dt = datetime.combine(date.today(), first_trigger_time)

        elapsed_minutes = (current_dt - first_trigger_dt).total_seconds() / 60.0

        if elapsed_minutes < 3:  # 至少观察3分钟
            return False

        observations = observation_data['observations']
        if len(observations) < 2:  # 至少需要2个观察点
            return False

        # 持续性验证：要求至少有2-3次都维持在较高水平
        high_level_count = 0
        for obs in observations:
            # 检查是否维持在动态阈值的50%以上
            wra_ratio = obs['wra'] / obs['wra_threshold'] if obs['wra_threshold'] > 0 else 0
            ct_ratio = obs['ct'] / obs['ct_threshold'] if obs['ct_threshold'] > 0 else 0

            if wra_ratio >= 0.5 and ct_ratio >= 0.5:
                high_level_count += 1

        # 至少有2次维持高水平才确认
        return high_level_count >= 2
    
    def cleanup_expired(self, current_time):
        """清理过期的观察记录"""
        from datetime import datetime, date, timedelta
        if isinstance(current_time, datetime):
            current_dt = current_time
        else:
            current_dt = datetime.combine(date.today(), current_time)
        
        expired_stocks = []
        for stock_name, data in self.pool.items():
            first_trigger_time = data['first_trigger_time']
            if isinstance(first_trigger_time, datetime):
                first_trigger_dt = first_trigger_time
            else:
                first_trigger_dt = datetime.combine(date.today(), first_trigger_time)
            
            elapsed_minutes = (current_dt - first_trigger_dt).total_seconds() / 60.0
            if elapsed_minutes > self.observation_minutes:
                expired_stocks.append(stock_name)
        
        for stock_name in expired_stocks:
            del self.pool[stock_name]


class StockFlowIgnitionDetector:
    """V7.0 个股资金流信号检测器 - “爆发点火”与“持续攻击”双模型矩阵版"""

    def __init__(self):
        # V5.1 及之前版本所需属性
        self.previous_snapshot = None
        self.market_pulse_pool = MarketPulseDataPool(SLIDING_WINDOW_MINUTES)
        self.observation_pool = ObservationPool()

        # V7.0 新增：用于“持续攻击”信号检测的长时间序列数据
        self.long_term_stock_data = {}  # {stock_name: [datapoint_1, datapoint_2, ...]}

        self.ignition_thresholds = {
            'min_rank': MIN_RANK_THRESHOLD,
            'min_pf': MIN_PF_THRESHOLD,
            'wra_multiplier': WRA_MULTIPLIER,
            'ct_multiplier': CT_MULTIPLIER,
            'min_score': 7.0
        }

        # V10.0 新增：板块映射缓存
        self.stock_board_mapping = None

    def _load_stock_board_mapping(self):
        """加载股票-板块映射关系"""
        import pickle
        import os

        try:
            # 尝试多个可能的路径
            possible_paths = [
                os.path.join('cache', 'stock_board_mapping.pkl'),
                os.path.join('..', 'cache', 'stock_board_mapping.pkl'),
                'cache/stock_board_mapping.pkl',
                '../cache/stock_board_mapping.pkl',
                os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'cache', 'stock_board_mapping.pkl'))
            ]

            for cache_file_path in possible_paths:
                if os.path.exists(cache_file_path):
                    with open(cache_file_path, 'rb') as f:
                        mapping_df = pickle.load(f)
                    print(f"✅ 成功加载股票-板块映射: {cache_file_path}")
                    return mapping_df

            print("⚠️ 股票-板块映射缓存不存在，板块因子功能将被禁用")
            print(f"⚠️ 当前工作目录: {os.getcwd()}")
            print(f"⚠️ 尝试的路径: {possible_paths}")
            return None
        except Exception as e:
            print(f"⚠️ 加载股票-板块映射失败: {e}，板块因子功能将被禁用")
            return None

    def _get_stock_sector_info(self, stock_name, stock_code=None, current_market_data=None):
        """V10.1 动态获取股票的板块信息 - 基于实时市场热度"""
        if not hasattr(self, 'stock_board_mapping') or self.stock_board_mapping is None:
            self.stock_board_mapping = self._load_stock_board_mapping()

        if self.stock_board_mapping is None or self.stock_board_mapping.empty:
            return {
                'sectors': [],
                'is_hot_sector': False,
                'is_mainline_sector': False,
                'sector_count': 0,
                'dynamic_heat_score': 0.0
            }

        try:
            # 通过股票代码查找（优先）
            if stock_code:
                # 修复pandas Series布尔值歧义问题
                code_mask = self.stock_board_mapping['代码'] == stock_code
                matching_rows = self.stock_board_mapping.loc[code_mask]
                stock_sectors = matching_rows['概念名称'].tolist()
            else:
                # 通过股票名称查找（备用）
                stock_sectors = []

            # V10.1 动态计算板块热度（基于当前市场数据）
            sector_heat_info = self._calculate_dynamic_sector_heat(stock_sectors, current_market_data)

            return {
                'sectors': stock_sectors,
                'is_hot_sector': sector_heat_info['is_hot'],
                'is_mainline_sector': sector_heat_info['is_mainline'],
                'sector_count': len(stock_sectors),
                'dynamic_heat_score': sector_heat_info['heat_score'],
                'sector_performance': sector_heat_info['performance']
            }

        except Exception as e:
            print(f"⚠️ 获取股票{stock_name}板块信息失败: {e}")
            return {
                'sectors': [],
                'is_hot_sector': False,
                'is_mainline_sector': False,
                'sector_count': 0,
                'dynamic_heat_score': 0.0
            }

    def _calculate_dynamic_sector_heat(self, stock_sectors, current_market_data):
        """V10.1 动态计算板块热度 - 基于实时市场表现"""
        if not stock_sectors or current_market_data is None or current_market_data.empty:
            return {
                'is_hot': False,
                'is_mainline': False,
                'heat_score': 0.0,
                'performance': {}
            }

        try:
            # 计算每个板块的实时表现
            sector_performances = []

            for sector in stock_sectors[:5]:  # 只分析前5个主要板块
                sector_perf = self._analyze_sector_realtime_performance(sector, current_market_data)
                sector_performances.append(sector_perf)

            if not sector_performances:
                return {
                    'is_hot': False,
                    'is_mainline': False,
                    'heat_score': 0.0,
                    'performance': {}
                }

            # 计算综合热度得分
            avg_heat_score = sum(p['heat_score'] for p in sector_performances) / len(sector_performances)
            max_heat_score = max(p['heat_score'] for p in sector_performances)

            # 动态判断热度阈值（基于市场整体表现）
            market_heat_threshold = self._calculate_market_heat_threshold(current_market_data)

            # 动态判断是否为热门/主线板块
            is_hot = max_heat_score >= market_heat_threshold['hot_threshold']
            is_mainline = max_heat_score >= market_heat_threshold['mainline_threshold']

            return {
                'is_hot': is_hot,
                'is_mainline': is_mainline,
                'heat_score': avg_heat_score,
                'performance': {
                    'max_heat': max_heat_score,
                    'avg_heat': avg_heat_score,
                    'sector_details': sector_performances[:3]  # 只保留前3个
                }
            }

        except Exception as e:
            return {
                'is_hot': False,
                'is_mainline': False,
                'heat_score': 0.0,
                'performance': {}
            }

    def _analyze_sector_realtime_performance(self, sector_name, current_market_data):
        """分析板块的实时表现"""
        try:
            if self.stock_board_mapping is None:
                return {'heat_score': 0.0, 'sector_name': sector_name}

            # 获取板块内股票
            sector_stocks = self.stock_board_mapping[
                self.stock_board_mapping['概念名称'] == sector_name
            ]['代码'].tolist()

            if not sector_stocks:
                return {'heat_score': 0.0, 'sector_name': sector_name}

            # 筛选出在当前市场数据中的板块股票
            sector_market_data = current_market_data[
                current_market_data['代码'].isin(sector_stocks)
            ].copy()

            if sector_market_data.empty:
                return {'heat_score': 0.0, 'sector_name': sector_name}

            # 计算板块热度指标
            # 1. 平均涨跌幅
            avg_change = sector_market_data['今日涨跌幅'].mean()

            # 2. 资金净流入比例（正流入股票占比）
            positive_inflow_ratio = (sector_market_data['今日主力净流入-净额'] > 0).mean()

            # 3. 总资金流入强度
            total_inflow = sector_market_data['今日主力净流入-净额'].sum()
            avg_inflow = total_inflow / len(sector_market_data) if len(sector_market_data) > 0 else 0

            # 4. 前排股票占比（排名前100的股票占比）
            top_stocks_count = len(sector_market_data[sector_market_data.index < 100])
            top_ratio = top_stocks_count / len(sector_market_data) if len(sector_market_data) > 0 else 0

            # 综合热度得分计算（0-1之间）
            heat_score = (
                max(0, avg_change / 5.0) * 0.3 +  # 涨跌幅权重30%
                positive_inflow_ratio * 0.25 +     # 资金流入比例权重25%
                min(1.0, max(0, avg_inflow / 50000000)) * 0.25 +  # 平均流入强度权重25%
                top_ratio * 0.2  # 前排占比权重20%
            )

            return {
                'heat_score': min(1.0, heat_score),
                'sector_name': sector_name,
                'avg_change': avg_change,
                'positive_inflow_ratio': positive_inflow_ratio,
                'avg_inflow': avg_inflow,
                'top_ratio': top_ratio,
                'stock_count': len(sector_market_data)
            }

        except Exception as e:
            return {'heat_score': 0.0, 'sector_name': sector_name}

    def _calculate_market_heat_threshold(self, current_market_data):
        """动态计算市场热度阈值"""
        try:
            if current_market_data is None or current_market_data.empty:
                return {
                    'hot_threshold': 0.6,
                    'mainline_threshold': 0.8
                }

            # 分析市场整体表现
            market_avg_change = current_market_data['今日涨跌幅'].mean()
            market_positive_ratio = (current_market_data['今日涨跌幅'] > 0).mean()

            # 根据市场整体表现动态调整阈值
            if market_avg_change >= 2.0 and market_positive_ratio >= 0.7:
                # 强势市场：提高阈值
                hot_threshold = 0.7
                mainline_threshold = 0.85
            elif market_avg_change >= 1.0 and market_positive_ratio >= 0.6:
                # 正常市场：标准阈值
                hot_threshold = 0.6
                mainline_threshold = 0.8
            elif market_avg_change >= 0:
                # 弱势市场：降低阈值
                hot_threshold = 0.5
                mainline_threshold = 0.7
            else:
                # 下跌市场：大幅降低阈值
                hot_threshold = 0.4
                mainline_threshold = 0.6

            return {
                'hot_threshold': hot_threshold,
                'mainline_threshold': mainline_threshold,
                'market_avg_change': market_avg_change,
                'market_positive_ratio': market_positive_ratio
            }

        except Exception as e:
            return {
                'hot_threshold': 0.6,
                'mainline_threshold': 0.8
            }

    def _analyze_sector_leadership(self, stock_name, stock_code, current_market_data, sector_info):
        """分析股票在板块内的龙头地位"""
        if not sector_info['sectors'] or current_market_data is None or current_market_data.empty:
            return {
                'is_sector_leader': False,
                'sector_rank': None,
                'sector_leadership_score': 0.0,
                'leadership_reason': '无板块信息或市场数据'
            }

        try:
            # 获取当前股票的资金流入
            # 修复pandas Series布尔值歧义问题
            name_mask = current_market_data['名称'] == stock_name
            current_stock_data = current_market_data.loc[name_mask]
            if current_stock_data.empty:
                return {
                    'is_sector_leader': False,
                    'sector_rank': None,
                    'sector_leadership_score': 0.0,
                    'leadership_reason': '未找到当前股票数据'
                }

            current_inflow = current_stock_data.iloc[0]['今日主力净流入-净额']

            # 分析每个板块内的排名
            leadership_scores = []
            sector_ranks = []

            for sector in sector_info['sectors'][:3]:  # 只分析前3个主要板块
                sector_leadership = self._calculate_sector_leadership_score(
                    stock_name, stock_code, current_inflow, sector, current_market_data
                )
                leadership_scores.append(sector_leadership['score'])
                sector_ranks.append(sector_leadership['rank'])

            if not leadership_scores:
                return {
                    'is_sector_leader': False,
                    'sector_rank': None,
                    'sector_leadership_score': 0.0,
                    'leadership_reason': '无有效板块数据'
                }

            # 计算综合龙头得分
            max_score = max(leadership_scores)
            best_rank = min(sector_ranks) if sector_ranks else None

            # 龙头判断标准
            is_sector_leader = False
            leadership_reason = ""

            if max_score >= 0.8:  # 绝对龙头
                is_sector_leader = True
                leadership_reason = f"板块绝对龙头(得分{max_score:.2f})"
            elif max_score >= 0.6 and best_rank and best_rank <= 3:  # 前3名且得分较高
                is_sector_leader = True
                leadership_reason = f"板块前排龙头(排名{best_rank}，得分{max_score:.2f})"
            elif best_rank and best_rank == 1:  # 板块第1名
                is_sector_leader = True
                leadership_reason = f"板块资金第1名(得分{max_score:.2f})"
            else:
                leadership_reason = f"非板块龙头(最佳排名{best_rank}，得分{max_score:.2f})"

            return {
                'is_sector_leader': is_sector_leader,
                'sector_rank': best_rank,
                'sector_leadership_score': max_score,
                'leadership_reason': leadership_reason
            }

        except Exception as e:
            return {
                'is_sector_leader': False,
                'sector_rank': None,
                'sector_leadership_score': 0.0,
                'leadership_reason': f'分析失败: {e}'
            }

    def _calculate_sector_leadership_score(self, stock_name, stock_code, current_inflow, sector_name, current_market_data):
        """计算股票在特定板块内的龙头得分"""
        try:
            if self.stock_board_mapping is None:
                return {'score': 0.0, 'rank': None}

            # 获取该板块内的所有股票代码
            sector_stocks = self.stock_board_mapping[
                self.stock_board_mapping['概念名称'] == sector_name
            ]['代码'].tolist()

            if not sector_stocks:
                return {'score': 0.0, 'rank': None}

            # 从市场数据中筛选出板块内股票
            sector_market_data = current_market_data[
                current_market_data['代码'].isin(sector_stocks)
            ].copy()

            if sector_market_data.empty:
                return {'score': 0.0, 'rank': None}

            # 按资金流入排序
            sector_market_data = sector_market_data.sort_values(
                by='今日主力净流入-净额', ascending=False
            )

            # 找到当前股票在板块内的排名
            stock_rank = None
            for i, row in sector_market_data.iterrows():
                if row['名称'] == stock_name:
                    stock_rank = sector_market_data.index.get_loc(i) + 1
                    break

            if stock_rank is None:
                return {'score': 0.0, 'rank': None}

            total_stocks = len(sector_market_data)

            # 计算龙头得分
            if stock_rank == 1:
                # 第1名：检查领先优势
                if total_stocks >= 2:
                    first_inflow = sector_market_data.iloc[0]['今日主力净流入-净额']
                    second_inflow = sector_market_data.iloc[1]['今日主力净流入-净额']
                    if second_inflow > 0:
                        lead_ratio = first_inflow / second_inflow
                        if lead_ratio >= 2.0:
                            score = 1.0  # 绝对龙头
                        elif lead_ratio >= 1.5:
                            score = 0.8  # 明显龙头
                        else:
                            score = 0.6  # 微弱龙头
                    else:
                        score = 0.9  # 唯一正流入
                else:
                    score = 0.7  # 板块内唯一股票
            elif stock_rank <= 3:
                # 前3名
                score = max(0.3, 0.8 - (stock_rank - 1) * 0.2)
            elif stock_rank <= 5:
                # 前5名
                score = max(0.1, 0.4 - (stock_rank - 4) * 0.1)
            else:
                # 其他
                score = max(0.0, 0.2 - (stock_rank - 6) * 0.02)

            return {'score': score, 'rank': stock_rank}

        except Exception as e:
            return {'score': 0.0, 'rank': None}

    def _calculate_sector_factor(self, sector_info, leadership_info, current_market_data=None):
        """V10.1 动态计算板块因子 - 基于实时市场数据而非固定阈值"""

        # 基础倍数（无调整）
        base_multiplier = 1.0

        # V10.1 动态板块热度加分（基于实时表现）
        sector_multiplier = base_multiplier
        heat_score = sector_info.get('dynamic_heat_score', 0.0)

        if sector_info['is_mainline_sector'] and heat_score >= 0.7:
            # 高热度主线板块：大幅降低阈值
            sector_multiplier = 0.5  # 降低50%
        elif sector_info['is_mainline_sector']:
            # 一般主线板块：适度降低阈值
            sector_multiplier = 0.7  # 降低30%
        elif sector_info['is_hot_sector'] and heat_score >= 0.6:
            # 高热度热门板块：适度降低阈值
            sector_multiplier = 0.65  # 降低35%
        elif sector_info['is_hot_sector']:
            # 一般热门板块：小幅降低阈值
            sector_multiplier = 0.8  # 降低20%
        elif heat_score >= 0.5:
            # 动态热门板块：基于实时热度
            sector_multiplier = 1.0 - (heat_score - 0.5) * 0.4  # 最多降低20%

        # V10.1 动态龙头地位加分（基于实时排名）
        leadership_multiplier = base_multiplier
        if leadership_info['is_sector_leader']:
            leadership_score = leadership_info['sector_leadership_score']
            sector_rank = leadership_info.get('sector_rank', 999)

            # 动态调整龙头加分（考虑排名和得分）
            if leadership_score >= 0.8 and sector_rank == 1:
                # 绝对龙头且排名第一：大幅降低阈值
                leadership_multiplier = 0.4  # 降低60%
            elif leadership_score >= 0.7 and sector_rank <= 2:
                # 强势龙头且前2名：适度降低阈值
                leadership_multiplier = 0.6  # 降低40%
            elif leadership_score >= 0.6 and sector_rank <= 3:
                # 明显龙头且前3名：小幅降低阈值
                leadership_multiplier = 0.75  # 降低25%
            elif sector_rank <= 5:
                # 前5名：微调
                leadership_multiplier = 0.9  # 降低10%

        # V10.1 动态概念丰富度加分
        concept_multiplier = base_multiplier
        sector_count = sector_info['sector_count']
        if sector_count >= 8:
            # 概念非常丰富：适度降低阈值
            concept_multiplier = 0.85  # 降低15%
        elif sector_count >= 5:
            # 概念丰富：小幅降低阈值
            concept_multiplier = 0.92  # 降低8%
        elif sector_count >= 3:
            # 概念适中：微调
            concept_multiplier = 0.97  # 降低3%

        # 综合计算最终倍数
        final_wra_multiplier = sector_multiplier * leadership_multiplier * concept_multiplier
        final_ct_multiplier = sector_multiplier * leadership_multiplier
        final_inflow_multiplier = sector_multiplier * leadership_multiplier

        # V10.1 动态下限设置（基于市场活跃度）
        market_activity = self._get_market_activity_level(current_market_data)
        if market_activity == 'high':
            min_wra, min_ct, min_inflow = 0.4, 0.5, 0.5
        elif market_activity == 'normal':
            min_wra, min_ct, min_inflow = 0.3, 0.4, 0.4
        else:  # low activity
            min_wra, min_ct, min_inflow = 0.2, 0.3, 0.3

        final_wra_multiplier = max(min_wra, final_wra_multiplier)
        final_ct_multiplier = max(min_ct, final_ct_multiplier)
        final_inflow_multiplier = max(min_inflow, final_inflow_multiplier)

        return {
            'wra_multiplier': final_wra_multiplier,
            'ct_multiplier': final_ct_multiplier,
            'inflow_multiplier': final_inflow_multiplier,
            'sector_info': sector_info,
            'leadership_info': leadership_info,
            'adjustment_reason': self._generate_dynamic_adjustment_reason(
                sector_info, leadership_info, final_wra_multiplier, heat_score
            )
        }

    def _get_market_activity_level(self, current_market_data):
        """获取市场活跃度等级"""
        if current_market_data is None or current_market_data.empty:
            return 'normal'

        try:
            # 计算市场活跃度指标
            avg_change = current_market_data['今日涨跌幅'].mean()
            positive_ratio = (current_market_data['今日涨跌幅'] > 0).mean()

            if avg_change >= 1.5 and positive_ratio >= 0.7:
                return 'high'
            elif avg_change <= -1.0 or positive_ratio <= 0.3:
                return 'low'
            else:
                return 'normal'
        except:
            return 'normal'

    def _generate_dynamic_adjustment_reason(self, sector_info, leadership_info, final_multiplier, heat_score):
        """生成动态阈值调整原因说明"""
        reasons = []

        # 板块热度相关
        if sector_info['is_mainline_sector']:
            if heat_score >= 0.7:
                reasons.append("高热度主线板块")
            else:
                reasons.append("主线板块")
        elif sector_info['is_hot_sector']:
            if heat_score >= 0.6:
                reasons.append("高热度热门板块")
            else:
                reasons.append("热门板块")
        elif heat_score >= 0.5:
            reasons.append(f"动态热门板块(热度{heat_score:.2f})")

        # 龙头地位相关
        if leadership_info['is_sector_leader']:
            score = leadership_info['sector_leadership_score']
            rank = leadership_info.get('sector_rank', 999)
            if score >= 0.8 and rank == 1:
                reasons.append("绝对龙头")
            elif score >= 0.7 and rank <= 2:
                reasons.append("强势龙头")
            elif score >= 0.6 and rank <= 3:
                reasons.append("明显龙头")
            else:
                reasons.append("板块龙头")

        # 概念丰富度
        sector_count = sector_info['sector_count']
        if sector_count >= 8:
            reasons.append("概念丰富")
        elif sector_count >= 5:
            reasons.append("多概念")

        if reasons:
            adjustment_pct = (1 - final_multiplier) * 100
            return f"{'+'.join(reasons)}(阈值降低{adjustment_pct:.0f}%)"
        else:
            return "无特殊调整"

    def _generate_adjustment_reason(self, sector_info, leadership_info, final_multiplier):
        """生成阈值调整原因说明"""
        reasons = []

        if sector_info['is_mainline_sector']:
            reasons.append("主线板块")
        elif sector_info['is_hot_sector']:
            reasons.append("热门板块")

        if leadership_info['is_sector_leader']:
            score = leadership_info['sector_leadership_score']
            if score >= 0.8:
                reasons.append("绝对龙头")
            elif score >= 0.6:
                reasons.append("明显龙头")
            else:
                reasons.append("板块龙头")

        if sector_info['sector_count'] >= 5:
            reasons.append("概念丰富")

        if reasons:
            adjustment_pct = (1 - final_multiplier) * 100
            return f"{'+'.join(reasons)}(阈值降低{adjustment_pct:.0f}%)"
        else:
            return "无特殊调整"

    def _update_long_term_data(self, current_data, current_time):
        """V7.0 - 更新并维护用于持续攻击信号检测的长时间窗口数据"""
        from datetime import datetime, date, timedelta

        current_dt = datetime.combine(date.today(), current_time) if not isinstance(current_time,
                                                                                    datetime) else current_time
        cutoff_dt = current_dt - timedelta(minutes=SUSTAINED_ATTACK_WINDOW_MINUTES)

        for idx, stock in current_data.iterrows():
            stock_name = stock['名称']

            if stock_name not in self.long_term_stock_data:
                self.long_term_stock_data[stock_name] = []

            data_point = {
                'timestamp': current_dt,
                'rank': idx + 1,
                'net_inflow': stock['今日主力净流入-净额'],
                'change_pct': stock.get('今日涨跌幅', 0.0)
            }
            self.long_term_stock_data[stock_name].append(data_point)

            self.long_term_stock_data[stock_name] = [
                p for p in self.long_term_stock_data[stock_name] if p['timestamp'] >= cutoff_dt
            ]

    def _calculate_sustained_attack_score(self, cumulative_inflow, benchmark_inflow, stability_ratio, slope):
        """V7.0 - 计算“持续攻击信号”的综合评分"""
        base_score = 5.0

        # 资金增量得分 (最高2.5分)
        inflow_ratio = cumulative_inflow / benchmark_inflow if benchmark_inflow > 0 else 1
        inflow_bonus = min(2.5, (inflow_ratio - 1) * 0.5)

        # 稳定性得分 (最高1.5分)
        stability_bonus = min(1.5, (stability_ratio - SUSTAINED_INFLOW_STABILITY_RATIO) / (
                    1 - SUSTAINED_INFLOW_STABILITY_RATIO) * 1.5)

        # 排名趋势得分 (最高1.0分)
        slope_bonus = min(1.0, abs(slope / SUSTAINED_RANK_SLOPE_THRESHOLD) * 0.5)

        total_score = base_score + inflow_bonus + stability_bonus + slope_bonus
        return min(10.0, total_score)

    def _create_sustained_attack_signal(self, stock_name, stock_data, history, cumulative_inflow, stability_ratio,
                                        slope, benchmark_inflow):
        """V7.0 - 创建“持续攻击信号”对象"""
        start_time = history[0]['timestamp'].time()
        end_time = history[-1]['timestamp'].time()
        duration = (history[-1]['timestamp'] - history[0]['timestamp']).total_seconds() / 60.0

        score = self._calculate_sustained_attack_score(cumulative_inflow, benchmark_inflow, stability_ratio, slope)

        return {
            'stock_name': stock_name,
            'stock_code': stock_data.get('代码', 'N/A'),
            'signal_type': f"持续攻击型",
            'time': end_time,
            'start_time': start_time,
            'duration_minutes': f"{duration:.1f}",
            'start_rank': history[0]['rank'],
            'end_rank': history[-1]['rank'],
            'cumulative_inflow': cumulative_inflow,
            'inflow_stability': stability_ratio,
            'rank_slope': slope,
            'price_response': stock_data.get('今日涨跌幅', 0.0),
            'score': score,
            'net_inflow': stock_data['今日主力净流入-净额']
        }

    def _detect_sustained_attack_signals(self, current_data):
        """V7.0 - 检测“持续攻击信号”"""
        sustained_signals = []
        if len(current_data) < SUSTAINED_CUMULATIVE_INFLOW_RANK_BENCHMARK:
            return []

        benchmark_inflow = current_data.iloc[SUSTAINED_CUMULATIVE_INFLOW_RANK_BENCHMARK - 1]['今日主力净流入-净额']

        for stock_name, history in self.long_term_stock_data.items():
            if len(history) < SUSTAINED_MIN_DATA_POINTS:
                continue

            # 修复pandas Series布尔值歧义问题
            name_mask = current_data['名称'] == stock_name
            current_stock_info_list = current_data.loc[name_mask]
            if current_stock_info_list.empty:
                continue

            current_stock_info = current_stock_info_list.iloc[0]
            current_rank = current_stock_info_list.index[0] + 1

            if current_rank > SUSTAINED_RANK_THRESHOLD:
                continue

            start_inflow = history[0]['net_inflow']
            cumulative_inflow = current_stock_info['今日主力净流入-净额'] - start_inflow
            if cumulative_inflow < benchmark_inflow:
                continue

            positive_intervals = sum(
                1 for i in range(1, len(history)) if history[i]['net_inflow'] > history[i - 1]['net_inflow'])
            stability_ratio = positive_intervals / (len(history) - 1) if len(history) > 1 else 1.0
            if stability_ratio < SUSTAINED_INFLOW_STABILITY_RATIO:
                continue

            try:
                timestamps_numeric = np.array(
                    [(p['timestamp'] - history[0]['timestamp']).total_seconds() / 60.0 for p in history])
                ranks = np.array([p['rank'] for p in history])
                if len(np.unique(timestamps_numeric)) < 2: continue  # 无法进行线性回归

                slope, _ = np.polyfit(timestamps_numeric, ranks, 1)
                if slope > SUSTAINED_RANK_SLOPE_THRESHOLD:
                    continue
            except (np.linalg.LinAlgError, ValueError):
                continue

            # 如果一只股票同时满足了爆发和持续的条件，为了避免重复报告，可以优先报告爆发信号
            # 这里检查它是否已在观察池中等待确认，如果是，则暂时不报持续信号
            if stock_name in self.observation_pool.pool and not self.observation_pool.pool[stock_name]['confirmed']:
                continue

            signal = self._create_sustained_attack_signal(
                stock_name, current_stock_info, history,
                cumulative_inflow, stability_ratio, slope, benchmark_inflow
            )
            sustained_signals.append(signal)

        return sustained_signals

    def detect_ignition_signals(self, current_data, current_time):
        """V7.0 - 检测“爆发点火”与“持续攻击”两种信号"""
        if self.previous_snapshot is None:
            self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
            self._update_long_term_data(current_data, current_time)
            return []

        # --- 数据更新 ---
        self.market_pulse_pool.cleanup_old_data(current_time)
        dynamic_thresholds = self.market_pulse_pool.get_dynamic_thresholds(current_data, current_time)  # V9.0 传递市场数据和时间
        self.observation_pool.cleanup_expired(current_time)
        self._update_long_term_data(current_data, current_time)

        # --- 模型一：“爆发点火”信号检测 ---
        benchmark_inflow = 0
        if len(current_data) > 50:
            benchmark_inflow = current_data.iloc[49]['今日主力净流入-净额']

        for idx, stock in current_data.iterrows():
            if idx >= 500: break
            stock_name = stock['名称']
            current_rank = idx + 1
            current_inflow = stock['今日主力净流入-净额']
            previous_data = self._find_previous_stock_data(stock_name)
            if previous_data is None: continue

            from datetime import datetime, date
            current_dt = datetime.combine(date.today(), current_time) if not isinstance(current_time,
                                                                                        datetime) else current_time
            previous_timestamp = previous_data.get('timestamp', current_time)
            previous_dt = datetime.combine(date.today(), previous_timestamp) if not isinstance(previous_timestamp,
                                                                                               datetime) else previous_timestamp
            time_delta_seconds = (current_dt - previous_dt).total_seconds()
            time_delta_minutes = time_delta_seconds / 60.0 if time_delta_seconds > 0 else 1.0

            rv = self.calculate_rank_velocity(current_rank, previous_data['rank'], time_delta_minutes)
            wra = self.calculate_weighted_rank_acceleration(rv, current_rank)
            ct = self.calculate_capital_thrust(current_inflow, previous_data['net_inflow'], time_delta_minutes)

            current_super_large = stock.get('今日超大单净流入-净额', 0.0)
            previous_super_large = previous_data.get('super_large_net_inflow', 0.0)
            pf = self.calculate_real_purity_of_force(current_inflow, previous_data['net_inflow'], current_super_large,
                                                     previous_super_large)

            self.market_pulse_pool.add_data_point(current_time, stock_name, wra, ct, current_rank)

            is_potential_signal = self._is_v5_ignition_signal(wra, ct, pf, current_rank, dynamic_thresholds, stock,
                                                              previous_data, stock_name, current_time)

            if is_potential_signal and benchmark_inflow > 0:
                if current_inflow < benchmark_inflow * MIN_RANK_BENCHMARK_RATIO:
                    is_potential_signal = False

            if is_potential_signal:
                enhanced_pf = self._calculate_enhanced_purity_of_force_v51(pf)
                self.observation_pool.add_candidate(stock_name, current_time, wra, ct, enhanced_pf, current_rank,
                                                    dynamic_thresholds)

        ignition_signals = []
        for stock_name in list(self.observation_pool.pool.keys()):
            if self.observation_pool.check_persistence(stock_name, current_time):
                # 修复pandas Series布尔值歧义问题
                name_mask = current_data['名称'] == stock_name
                stock_data_list = current_data.loc[name_mask]
                if stock_data_list.empty: continue

                stock_data = stock_data_list.iloc[0]
                current_rank = stock_data_list.index[0] + 1
                previous_data = self._find_previous_stock_data(stock_name)

                latest_observation = self.observation_pool.pool[stock_name]['observations'][-1]
                wra = latest_observation['wra']
                ct = latest_observation['ct']
                pf = latest_observation['pf']

                if self._validate_volume_resonance(stock_data, previous_data, stock_name):
                    rv = self.calculate_rank_velocity(current_rank, previous_data['rank'])
                    signal = self._create_v5_ignition_signal(
                        stock_name, stock_data, current_rank, previous_data,
                        wra, ct, pf, rv, current_time, dynamic_thresholds
                    )
                    ignition_signals.append(signal)
                    self.observation_pool.pool[stock_name]['confirmed'] = True

        # --- 模型二：“持续攻击”信号检测 ---
        sustained_signals = self._detect_sustained_attack_signals(current_data)

        # --- 数据更新与返回 ---
        self.previous_snapshot = self._create_data_snapshot(current_data, current_time)

        # 合并两种信号并返回
        all_signals = ignition_signals + sustained_signals
        return all_signals

    def calculate_rank_velocity(self, current_rank, previous_rank, time_delta_minutes=1):
        """计算排名速度 (Rank Velocity, RV)"""
        if previous_rank is None or current_rank is None:
            return 0
        return (previous_rank - current_rank) / time_delta_minutes

    def calculate_weighted_rank_acceleration(self, rank_velocity, current_rank):
        """计算排名权重加速度 (Weighted Rank Acceleration, WRA)"""
        if current_rank <= 0:
            return 0
        return rank_velocity * (1 / current_rank)

    def calculate_capital_thrust(self, current_inflow, previous_inflow, time_delta_minutes=1):
        """计算资金冲击力 (Capital Thrust, CT) - 单位：万元/分钟"""
        if previous_inflow is None or current_inflow is None:
            return 0
        return (current_inflow - previous_inflow) / 1e4 / time_delta_minutes

    def calculate_purity_of_force(self, super_large_inflow_change, total_inflow_change):
        """计算主力纯度 (Purity of Force, PF)"""
        if total_inflow_change <= 0:
            return 0
        return super_large_inflow_change / total_inflow_change

    def _calculate_enhanced_purity_of_force_v51(self, pf):
        """V5.1 增强版主力纯度计算 - 重新定义PF指标"""
        return pf  # 基础PF值，后续通过_is_pf_in_healthy_range进行健康度判断

    def _is_pf_in_healthy_range(self, pf):
        """V5.1 判断主力纯度是否在健康区间

        健康区间定义：
        - 70% < PF < 150%：健康状态，超大单主导，市场分歧不大
        - PF > 150%：可能存在出货风险，需要谨慎
        - PF < 70%：主力意图不够明确
        """
        return 0.7 < pf < 1.5

    def _validate_volume_resonance(self, current_data, previous_data, stock_name):
        """V5.1 成交量验证 - 实现价量资金三重共振（接口预留版）

        验证逻辑：
        1. 检查全局开关：如果ENABLE_VOLUME_VALIDATION=False，跳过验证
        2. 成交量验证：信号触发时，分钟成交量是过去30分钟平均成交量的N倍以上
        3. 换手率验证：如果有换手率数据，进行额外验证
        4. 价量资金共振：价格、成交量、资金流三者协同上涨

        返回：
        - True: 通过验证或已跳过验证
        - False: 未通过验证
        """
        if not ENABLE_VOLUME_VALIDATION:
            return True

        volume_data = self._extract_volume_data(current_data)
        previous_volume_data = self._extract_volume_data_from_snapshot(previous_data)

        if volume_data is None:
            return True

        return self._perform_volume_validation(volume_data, previous_volume_data, stock_name)

    def _extract_volume_data(self, data):
        """从数据中提取成交量相关信息"""
        volume_info = {}

        for field_name, mapped_name in VOLUME_FIELD_MAPPING.items():
            if field_name in data.index and pd.notna(data[field_name]):
                volume_info[mapped_name] = data[field_name]

        return volume_info if volume_info else None

    def _extract_volume_data_from_snapshot(self, snapshot_data):
        """从历史快照中提取成交量数据"""
        if not snapshot_data:
            return None

        volume_info = {}
        if 'volume' in snapshot_data:
            volume_info['volume'] = snapshot_data['volume']
        if 'turnover' in snapshot_data:
            volume_info['turnover'] = snapshot_data['turnover']

        return volume_info if volume_info else None

    def _perform_volume_validation(self, current_volume, previous_volume, stock_name):
        """执行具体的成交量验证逻辑"""
        if 'volume' in current_volume and previous_volume and 'volume' in previous_volume:
            current_vol = current_volume['volume']
            previous_vol = previous_volume['volume']

            if previous_vol > 0:
                volume_multiplier = current_vol / previous_vol
                if volume_multiplier < VOLUME_MULTIPLIER_THRESHOLD:
                    return False

        if ('turnover' in current_volume and previous_volume and
                'turnover' in previous_volume):
            current_turnover = current_volume['turnover']
            previous_turnover = previous_volume['turnover']

            if previous_turnover > 0:
                turnover_multiplier = current_turnover / previous_turnover
                if turnover_multiplier < 1.2:
                    return False

        return True

    def calculate_real_purity_of_force(self, current_inflow, previous_inflow, current_super_large,
                                       previous_super_large):
        """V4.0 计算真实的、基于增量的主力纯度 (Purity of Force, PF)"""
        if previous_inflow is None or previous_super_large is None:
            return 0.0

        total_inflow_change = current_inflow - previous_inflow
        super_large_inflow_change = current_super_large - previous_super_large

        if total_inflow_change <= 1e-6:
            return 0.0

        pf = super_large_inflow_change / total_inflow_change
        return pf

    def _is_v5_ignition_signal(self, wra, ct, pf, current_rank, dynamic_thresholds, current_data, previous_data,
                               stock_name, current_time):
        """V10.0 板块增强点火信号判断 - 加入板块因子和龙头加分机制"""

        # 使用自适应排名阈值
        adaptive_rank_threshold = self.ignition_thresholds['min_rank']
        if current_rank >= adaptive_rank_threshold:
            return False

        # 获取自适应阈值
        adaptive_wra_threshold = dynamic_thresholds['dynamic_wra_threshold']
        adaptive_ct_threshold = dynamic_thresholds['market_based_ct_threshold']
        adaptive_inflow_threshold = dynamic_thresholds['adaptive_inflow_threshold']
        time_factor = dynamic_thresholds['time_factor']
        market_activity = dynamic_thresholds['market_activity_level']

        # V10.1 新增：动态板块因子分析
        stock_code = current_data.get('代码', None)

        # 获取当前市场数据用于动态板块分析
        current_market_data = dynamic_thresholds.get('current_market_data', None)
        sector_info = self._get_stock_sector_info(stock_name, stock_code, current_market_data)
        leadership_info = self._analyze_sector_leadership(
            stock_name, stock_code, current_market_data, sector_info
        )

        # V10.1 动态板块因子调整阈值
        sector_factor = self._calculate_sector_factor(sector_info, leadership_info, current_market_data)

        # 应用板块因子调整阈值
        adjusted_wra_threshold = adaptive_wra_threshold * sector_factor['wra_multiplier']
        adjusted_ct_threshold = adaptive_ct_threshold * sector_factor['ct_multiplier']
        adjusted_inflow_threshold = adaptive_inflow_threshold * sector_factor['inflow_multiplier']

        # V10.0 前排特殊处理 - 基于自适应阈值 + 板块加分
        is_top5_leader = current_rank <= 5
        is_top1_leader = current_rank == 1

        if is_top1_leader:
            # 第1名：豁免条件基于自适应阈值
            total_inflow_change = current_data['今日主力净流入-净额'] - previous_data['net_inflow']
            ct_exemption_threshold = adjusted_ct_threshold * 1.5
            inflow_exemption_threshold = adjusted_inflow_threshold * 5

            if ct > ct_exemption_threshold and total_inflow_change >= inflow_exemption_threshold:
                # 豁免WRA检查
                pass
            else:
                # 第1名降级：WRA要求降低90%
                wra_threshold = adjusted_wra_threshold * 0.1
                if wra <= wra_threshold:
                    return False
        elif is_top5_leader:
            # 前5名：WRA要求降低50%
            wra_threshold = adjusted_wra_threshold * 0.5
            if wra <= wra_threshold:
                return False
        else:
            # 普通股票：使用调整后的WRA阈值
            if wra <= adjusted_wra_threshold:
                return False

        # V10.0 CT阈值检查 - 使用板块调整后的CT阈值
        if ct <= adjusted_ct_threshold:
            return False

        # PF检查 - 应用时间因子
        enhanced_pf = self._calculate_enhanced_purity_of_force_v51(pf)
        adaptive_pf_threshold = MIN_PF_THRESHOLD * time_factor
        if enhanced_pf < adaptive_pf_threshold:
            return False

        # 增量资金检查 - 使用板块调整后的阈值
        total_inflow_change = current_data['今日主力净流入-净额'] - previous_data['net_inflow']
        if total_inflow_change < adjusted_inflow_threshold:
            return False

        return True

    def _calculate_ignition_score(self, wra, ct, pf, rank_jump, current_rank):
        """计算点火强度综合评分"""
        wra_score = min(10, wra * 10) * 0.4
        ct_score = min(10, ct / 1000) * 0.3
        pf_score = pf * 10 * 0.2
        jump_score = min(10, rank_jump / 50 * 10) * 0.1
        return wra_score + ct_score + pf_score + jump_score

    def _create_ignition_signal(self, stock_name, stock_data, current_rank, previous_data, wra, ct, pf, rv,
                                current_time):
        """创建点火信号对象（保留原版本兼容性）"""
        rank_jump = previous_data['rank'] - current_rank
        score = self._calculate_ignition_score(wra, ct, pf, rank_jump, current_rank)
        signal_type = self._classify_signal_type(current_time, ct, current_rank)
        price_response = stock_data.get('今日涨跌幅', 0)

        return {
            'stock_name': stock_name,
            'stock_code': stock_data.get('代码', 'N/A'),
            'signal_type': signal_type,
            'time': current_time,
            'rank_jump': rank_jump,
            'old_rank': previous_data['rank'],
            'new_rank': current_rank,
            'wra': wra,
            'ct': ct,
            'pf': pf,
            'price_response': price_response,
            'score': score,
            'net_inflow': stock_data['今日主力净流入-净额']
        }

    def _create_v5_ignition_signal(self, stock_name, stock_data, current_rank, previous_data, wra, ct, pf, rv,
                                   current_time, dynamic_thresholds):
        """V10.0 创建点火信号对象 - 增加板块信息"""
        rank_jump = previous_data['rank'] - current_rank
        score = self._calculate_v5_ignition_score(wra, ct, pf, current_rank, dynamic_thresholds)
        signal_type = self._classify_v5_signal_type(current_time, ct, current_rank, wra, pf)
        price_response = stock_data.get('今日涨跌幅', 0)

        # V10.0 新增：获取板块信息
        stock_code = stock_data.get('代码', None)
        sector_info = self._get_stock_sector_info(stock_name, stock_code)
        current_market_data = dynamic_thresholds.get('current_market_data', None)
        leadership_info = self._analyze_sector_leadership(
            stock_name, stock_code, current_market_data, sector_info
        )
        sector_factor = self._calculate_sector_factor(sector_info, leadership_info)

        return {
            'stock_name': stock_name,
            'stock_code': stock_data.get('代码', 'N/A'),
            'signal_type': f"{signal_type}+板块增强",
            'time': current_time,
            'rank_jump': rank_jump,
            'old_rank': previous_data['rank'],
            'new_rank': current_rank,
            'wra': wra,
            'ct': ct,
            'pf': pf,
            'price_response': price_response,
            'score': score,
            'net_inflow': stock_data['今日主力净流入-净额'],
            'dynamic_wra_threshold': dynamic_thresholds['dynamic_wra_threshold'],
            'dynamic_ct_threshold': dynamic_thresholds['dynamic_ct_threshold'],
            'wra_exceed_ratio': wra / (dynamic_thresholds['dynamic_wra_threshold'] * WRA_MULTIPLIER) if
            dynamic_thresholds['dynamic_wra_threshold'] > 0 else 0,
            'ct_exceed_ratio': ct / (dynamic_thresholds['dynamic_ct_threshold'] * CT_MULTIPLIER) if dynamic_thresholds[
                                                                                                        'dynamic_ct_threshold'] > 0 else 0,
            'market_data_points': dynamic_thresholds['data_points'],
            # V10.0 新增板块信息
            'sector_info': sector_info,
            'leadership_info': leadership_info,
            'sector_factor': sector_factor,
            'adjusted_wra_threshold': dynamic_thresholds['dynamic_wra_threshold'] * sector_factor['wra_multiplier'],
            'adjusted_ct_threshold': dynamic_thresholds['dynamic_ct_threshold'] * sector_factor['ct_multiplier'],
            'sector_adjustment_reason': sector_factor['adjustment_reason']
        }

    def _classify_signal_type(self, current_time, ct, current_rank):
        """分类信号类型（保留原版本兼容性）"""
        hour = current_time.hour if hasattr(current_time, 'hour') else 10

        if hour <= 10:
            return "盘中强攻型"
        elif hour >= 14:
            return "尾盘偷袭型"
        elif current_rank <= 10 and ct > 5000:
            return "龙头驱动型"
        else:
            return "盘中强攻型"

    def _classify_v5_signal_type(self, current_time, ct, current_rank, wra, pf):
        """V5.0 增强信号类型分类"""
        hour = current_time.hour if hasattr(current_time, 'hour') else 10
        minute = current_time.minute if hasattr(current_time, 'minute') else 30

        if hour == 9 and minute <= 45:
            base_type = "开盘抢筹型"
        elif hour <= 10:
            base_type = "早盘强攻型"
        elif 10 < hour < 11 or (13 <= hour < 14):
            base_type = "盘中突击型"
        elif hour >= 14 and minute >= 30:
            base_type = "尾盘偷袭型"
        else:
            base_type = "盘中强攻型"

        if current_rank <= 10 and ct > 50000 and pf > 0.8:
            intensity = "超级"
        elif current_rank <= 30 and ct > 20000 and pf > 0.7:
            intensity = "强力"
        elif current_rank <= 50 and wra > 1.0:
            intensity = "快速"
        else:
            intensity = "标准"

        return f"{intensity}{base_type}"

    def _calculate_v5_ignition_score(self, wra, ct, pf, current_rank, dynamic_thresholds):
        """V5.0 点火强度综合评分算法 (能量与技巧并重)"""
        base_score = 4.0

        wra_threshold = dynamic_thresholds['dynamic_wra_threshold'] * WRA_MULTIPLIER
        wra_exceed_ratio = wra / wra_threshold if wra_threshold > 0 else 1
        wra_bonus = min(2.0, (wra_exceed_ratio - 1) * 1.5)

        ct_threshold = dynamic_thresholds['dynamic_ct_threshold'] * CT_MULTIPLIER
        ct_exceed_ratio = ct / ct_threshold if ct_threshold > 0 else 1
        ct_bonus = min(1.5, (ct_exceed_ratio - 1) * 1.0)

        capped_pf = min(pf, 3.0)
        pf_bonus = 0
        if pf > MIN_PF_THRESHOLD:
            pf_bonus = (capped_pf - MIN_PF_THRESHOLD) / (3.0 - MIN_PF_THRESHOLD) * 1.5

        ct_absolute_bonus = 0
        if ct > 50000:
            ct_absolute_bonus = 3.0
        elif ct > 20000:
            ct_absolute_bonus = 2.0
        elif ct > 10000:
            ct_absolute_bonus = 1.0
        elif ct > 5000:
            ct_absolute_bonus = 0.5

        rank_bonus = max(0, (MIN_RANK_THRESHOLD - current_rank) / MIN_RANK_THRESHOLD)
        total_score = base_score + wra_bonus + ct_bonus + pf_bonus + rank_bonus + ct_absolute_bonus
        return min(10.0, total_score)

    def _create_data_snapshot(self, data, timestamp):
        """V5.1 创建包含超大单和成交量的精确数据快照（支持动态字段映射）"""
        snapshot = {}
        if '今日超大单净流入-净额' not in data.columns:
            data = data.copy()
            data['今日超大单净流入-净额'] = 0.0

        available_volume_fields = {}
        for field_name, mapped_name in VOLUME_FIELD_MAPPING.items():
            if field_name in data.columns:
                available_volume_fields[mapped_name] = field_name

        for idx, stock in data.iterrows():
            if idx >= 500: break
            snapshot_data = {
                'rank': idx + 1,
                'net_inflow': stock['今日主力净流入-净额'],
                'super_large_net_inflow': stock['今日超大单净流入-净额'],
                'timestamp': timestamp
            }
            for mapped_name, field_name in available_volume_fields.items():
                if pd.notna(stock[field_name]):
                    snapshot_data[mapped_name] = stock[field_name]
            snapshot[stock['名称']] = snapshot_data
        return snapshot

    def _find_previous_stock_data(self, stock_name):
        """查找股票的历史数据"""
        if self.previous_snapshot and stock_name in self.previous_snapshot:
            return self.previous_snapshot[stock_name]
        return None


class MainlineStrengthScorer:
    """主线强度评分器 - 根据盘面数据计算信号的主线强度分"""
    
    def __init__(self):
        pass
    
    def score_signal(self, signal, market_snapshot):
        """
        V10.2 - 计算信号的主线强度分 (绝对主线优先版)

        参数:
        - signal: 信号字典，包含股票名称、代码等信息
        - market_snapshot: 市场快照，包含当前盘面核心信息

        返回:
        - tuple: (主线强度分, 评级, 评分理由列表)
        """
        score = 0
        reasons = []

        stock_name = signal.get('股票名称', signal.get('stock_name', ''))
        stock_code = signal.get('代码', signal.get('stock_code', ''))

        # 获取股票板块信息
        sectors_info = get_stock_sectors(stock_name, stock_code)
        if not sectors_info:
            stock_sectors = set()
        else:
            stock_sectors = set(sectors_info.get('concepts', []) + sectors_info.get('industries', []))

        # =======================================================================
        # 第一步：【最高优先级】检查是否命中"绝对主线"
        # =======================================================================
        absolute_mainline_sectors = market_snapshot.get('absolute_mainline_sectors', set())
        limit_up_counts = market_snapshot.get('limit_up_counts_by_sector', {})

        if absolute_mainline_sectors and stock_sectors:
            # 找到股票所属板块与绝对主线板块的交集
            hit_sectors = stock_sectors.intersection(absolute_mainline_sectors)

            if hit_sectors:
                # 【修复】过滤TDX和无意义概念，选择有意义的板块作为代表
                from concept_sector_filter import is_meaningful_concept
                meaningful_hit_sectors = [sector for sector in hit_sectors if is_meaningful_concept(sector)]

                if meaningful_hit_sectors:
                    # 取第一个有意义的命中板块作为代表
                    hit_sector_name = meaningful_hit_sectors[0]

                    # 构建理由
                    hit_reason = f"命中绝对主线({hit_sector_name}"
                    # 附上该板块的涨停数或资金排名信息，让理由更充分
                    limit_up_count = limit_up_counts.get(hit_sector_name, 0)
                    if limit_up_count >= ABSOLUTE_MAINLINE_LIMIT_UP_THRESHOLD:
                        hit_reason += f"/{limit_up_count}家涨停)"
                    else:
                        hit_reason += "/资金前排)"

                    reasons.append(hit_reason)
                    score = ABSOLUTE_MAINLINE_SCORE
                    rating = "★★★ [绝对主线]"

                    # 直接返回，不再执行后续的复杂评分逻辑
                    return score, rating, reasons

        # =======================================================================
        # 第二步：【回退逻辑】如果没有命中绝对主线，则执行原有的、基于排行的评分体系
        # =======================================================================

        # 【V10.4 新增】0. 板块人气强度 (直接基于涨停数加分)
        limit_up_counts = market_snapshot.get('limit_up_counts_by_sector', {})
        max_limit_up_bonus = 0
        best_limit_up_sector = ""

        # 【修复】过滤TDX和无意义概念
        from concept_sector_filter import is_meaningful_concept

        for sector in stock_sectors:
            # 【新增】只考虑有意义的概念和行业
            if not is_meaningful_concept(sector):
                continue

            count = limit_up_counts.get(sector, 0)
            if count >= 4:
                bonus = 9
            elif count == 3:
                bonus = 7
            elif count == 2:
                bonus = 5
            else:
                bonus = 0

            if bonus > max_limit_up_bonus:
                max_limit_up_bonus = bonus
                best_limit_up_sector = sector

        if max_limit_up_bonus > 0:
            score += max_limit_up_bonus
            count = limit_up_counts.get(best_limit_up_sector, 0)
            reasons.append(f"板块人气高涨({best_limit_up_sector}/{count}家涨停)")

        # 1. 个股龙头地位 (+10分)
        stock_gap_leader = market_snapshot.get('stock_gap_leader', '')
        if stock_name == stock_gap_leader:
            score += 10
            reasons.append("个股断层龙头地位")

        # 2. 板块龙头地位 (+8分)
        sector_gap_leaders = market_snapshot.get('sector_gap_leaders', [])
        for sector in stock_sectors:
            # 【修复】只考虑有意义的概念和行业
            if sector in sector_gap_leaders and is_meaningful_concept(sector):
                score += 8
                reasons.append(f"板块断层龙头({sector})")
                break

        # 3. 板块资金排名 (最高5分)
        sector_rankings = market_snapshot.get('sector_rankings', {})
        max_ranking_score = 0
        best_ranking_sector = ""
        for sector in stock_sectors:
            if sector in sector_rankings:
                rank = sector_rankings[sector]
                if rank == 1: ranking_score = 5
                elif rank == 2: ranking_score = 4
                elif rank == 3: ranking_score = 3
                else: ranking_score = 0

                if ranking_score > max_ranking_score:
                    max_ranking_score = ranking_score
                    best_ranking_sector = sector

        if max_ranking_score > 0:
            score += max_ranking_score
            rank = sector_rankings[best_ranking_sector]
            reasons.append(f"板块资金排名第{rank}名({best_ranking_sector})")

        # 4. 板块梯队强度 - 连板 (最高5分)
        consecutive_board_leaders = market_snapshot.get('consecutive_board_leaders', {})
        max_consecutive_score = 0
        best_consecutive_sector = ""
        for sector in stock_sectors:
            if sector in consecutive_board_leaders:
                info = consecutive_board_leaders[sector]
                if info.get('type') == '连板数最多': consecutive_score = 5
                elif info.get('consecutive_days', 0) >= 3: consecutive_score = 4
                elif info.get('consecutive_days', 0) == 2: consecutive_score = 4
                else: consecutive_score = 0
                if consecutive_score > max_consecutive_score:
                    max_consecutive_score = consecutive_score
                    best_consecutive_sector = sector
        if max_consecutive_score > 0:
            score += max_consecutive_score
            reasons.append(f"连板梯队强度({best_consecutive_sector})")

        # 5. 板块梯队强度 - 涨停 (最高4分)
        limit_up_leaders_stats = market_snapshot.get('limit_up_leaders', {})
        max_limit_up_score = 0
        best_limit_up_sector = ""
        for sector in stock_sectors:
            if sector in limit_up_leaders_stats:
                info = limit_up_leaders_stats[sector]
                if info.get('type') == '涨停行业最多': limit_up_score = 4
                elif info.get('type') == '首板最多': limit_up_score = 3
                else: limit_up_score = 0
                if limit_up_score > max_limit_up_score:
                    max_limit_up_score = limit_up_score
                    best_limit_up_sector = sector
        if max_limit_up_score > 0:
            score += max_limit_up_score
            reasons.append(f"涨停梯队强度({best_limit_up_sector})")

        # 6. 板块资金加速 (+8分)
        accelerating_sectors = market_snapshot.get('accelerating_sectors', [])
        for sector in stock_sectors:
            # 【修复】只考虑有意义的概念和行业
            if sector in accelerating_sectors and is_meaningful_concept(sector):
                score += 8
                reasons.append(f"板块资金加速警报({sector})")
                break

        # 计算评级
        if score >= 10: rating = "★★★ [优先]"
        elif score >= 5: rating = "★★☆ [关注]"
        else: rating = "★☆☆ [观察]"

        # 如果没有匹配的主线，生成详细的股票板块信息
        if not reasons:
            sector_info_list = []
            if sectors_info and sectors_info.get('concepts'):
                # 【修复】过滤TDX和无意义概念
                meaningful_concepts = [c for c in sectors_info['concepts'] if is_meaningful_concept(c)]
                sector_info_list.extend(meaningful_concepts[:3])
            if sectors_info and sectors_info.get('industries'):
                # 【修复】过滤TDX和无意义行业
                meaningful_industries = [i for i in sectors_info['industries'] if is_meaningful_concept(i)]
                sector_info_list.extend(meaningful_industries[:2])
            if sector_info_list:
                reasons.append(f"股票板块({', '.join(sector_info_list)})不属于当前主线")
            else:
                reasons.append("未获取到或无有效板块信息")

        return score, rating, reasons




class HistoricalBreakthroughDetector:
    """历史突破信号检测器 - 检测"二次点火"和"横空出世"两种买入信号"""

    def __init__(self):
        # 状态追踪机制：存储上一个时间点的股票状态
        self.previous_stock_states = {}  # {stock_name: {'rank': int, 'gthi_has_signal': bool, 'gthi_days': int, 'net_inflow': float}}
        
        # 初始化主线强度评分器
        self.mainline_scorer = MainlineStrengthScorer()

    def _parse_gthi_string(self, gthi_text):
        """
        解析"大于历史资金流入"列的文本，返回历史天数

        参数:
        - gthi_text: "大于历史资金流入"列的文本值

        返回:
        - int: 历史天数
        """
        if pd.isna(gthi_text) or gthi_text == '' or gthi_text is None:
            return 0

        gthi_text = str(gthi_text).strip()

        if gthi_text == '是':
            return 101  # 突破了非常长期的记录，给予固定高权重天数

        # 匹配 '前XX天' 格式
        import re
        match = re.match(r'前(\d+)天', gthi_text)
        if match:
            return int(match.group(1))

        return 0

    def _calculate_comprehensive_score(self, main_net_inflow, historical_days, signal_type, stock_name=None, order_flow_tracker=None):
        """
        计算综合分数

        参数:
        - main_net_inflow: 主力净流入金额
        - historical_days: 历史天数
        - signal_type: 信号类型 ('二次点火', '横空出世', '突破强化')
        - stock_name: 股票名称，用于检查盘口异动
        - order_flow_tracker: 盘口异动追踪器

        返回:
        - float: 综合分数
        """
        # 类型系数映射
        type_coefficient_map = {
            '二次点火': 1.0,
            '横空出世': 1.2,
            '突破强化': 1.1
        }
        type_coefficient = type_coefficient_map.get(signal_type, 1.0)

        # 避免log函数值过大，先除以10000（单位：万元）
        if main_net_inflow <= 0:
            return 0.0

        try:
            # 基础综合分数 = math.log(主力净流入金额 / 10000) * (1 + 历史天数 / 100) * 类型系数
            base_score = math.log(main_net_inflow / 10000) * (1 + historical_days / 100) * type_coefficient

            # 盘口异动加分项
            order_flow_bonus = 0.0
            if stock_name and order_flow_tracker and stock_name in order_flow_tracker:
                signal_count = len(order_flow_tracker[stock_name])
                if signal_count >= 3:  # 密集盘口异动
                    # 盘口异动加分：每多一次信号加0.5分，最高加5分
                    order_flow_bonus = min(5.0, (signal_count - 2) * 0.5)
                    print(f"    【{stock_name}】盘口异动加分: +{order_flow_bonus:.1f}分 (共{signal_count}次大买盘)")

            final_score = base_score + order_flow_bonus
            return max(0.0, final_score)  # 确保分数不为负
        except (ValueError, OverflowError):
            return 0.0

    def detect_signals(self, stock_flow_data, current_time, market_snapshot=None, order_flow_tracker=None):
        """
        检测历史突破信号

        参数:
        - stock_flow_data: 当前时间点的个股资金流数据 (DataFrame)
        - current_time: 当前时间点
        - market_snapshot: 市场快照数据，包含盘面核心信息
        - order_flow_tracker: 盘口异动追踪器，用于优化现有信号

        返回:
        - list: 检测到的信号列表
        """
        if stock_flow_data is None or stock_flow_data.empty:
            return []

        signals = []
        current_stock_states = {}

        # 遍历当前时间点的所有股票
        for idx, row in stock_flow_data.iterrows():
            stock_name = row.get('名称', '')
            if not stock_name:
                continue

            # 获取当前状态
            current_rank = idx + 1  # 排名从1开始
            current_gthi = row.get('大于历史资金流入', '')
            current_main_inflow = row.get('今日主力净流入-净额', 0)

            # 解析当前状态的详细信息
            current_gthi_has_signal = current_gthi != '' and not pd.isna(current_gthi)
            current_gthi_days = self._parse_gthi_string(current_gthi)

            # 记录当前状态（扩展的状态结构）
            current_stock_states[stock_name] = {
                'rank': current_rank,
                'gthi_has_signal': current_gthi_has_signal,
                'gthi_days': current_gthi_days,
                'net_inflow': current_main_inflow
            }

            # 获取上一个时间点的状态
            previous_state = self.previous_stock_states.get(stock_name, {})
            previous_rank = previous_state.get('rank', None)
            previous_gthi_has_signal = previous_state.get('gthi_has_signal', False)
            previous_gthi_days = previous_state.get('gthi_days', 0)
            previous_net_inflow = previous_state.get('net_inflow', 0)

            # 为了兼容旧的状态格式，如果存在旧格式则转换
            if 'gthi_status' in previous_state and 'gthi_has_signal' not in previous_state:
                old_gthi_status = previous_state.get('gthi_status', '')
                previous_gthi_has_signal = old_gthi_status != '' and not pd.isna(old_gthi_status)
                previous_gthi_days = self._parse_gthi_string(old_gthi_status)

            # 检测信号一："二次点火" (Top-Tier Ignition)
            if (previous_rank is not None and  # 必须有历史状态
                previous_rank <= 10 and  # 上一个时间点排名在前10名
                not previous_gthi_has_signal and  # 上一个时间点没有"大于历史资金流入"标记
                current_rank <= 10 and   # 当前时间点排名仍在前10名
                current_gthi_has_signal):     # 当前时间点首次出现"大于历史资金流入"标记

                score = self._calculate_comprehensive_score(current_main_inflow, current_gthi_days, '二次点火', stock_name, order_flow_tracker)

                # 创建基础信号
                signal = {
                    '信号类型': '二次点火',
                    '股票名称': stock_name,
                    '当前排名': current_rank,
                    '主力净流入': current_main_inflow,
                    '突破周期': current_gthi,
                    '综合评分': score,
                    '信号时间': current_time,
                    '代码': str(row.get('代码', '')) if pd.notna(row.get('代码', '')) else ''
                }

                # 添加主线强度评分
                if market_snapshot:
                    mainline_score, rating, reasons = self.mainline_scorer.score_signal(signal, market_snapshot)
                    signal['主线强度分'] = mainline_score
                    signal['评级'] = rating
                    signal['评分理由'] = ' | '.join(reasons) if reasons and isinstance(reasons, list) else (str(reasons) if reasons else '无相关主线')

                    print(f"   计算结果: 主线强度分={mainline_score}, 评级={rating}")
                else:
                    signal['主线强度分'] = 0
                    signal['评级'] = '★☆☆ [观察]'
                    signal['评分理由'] = '无市场数据'

                signals.append(signal)

            # 检测信号二："横空出世" (Breakout Emergence)
            elif ((previous_rank is None or previous_rank > 50) and  # 上一个时间点不存在或排名在50名以外
                  current_rank <= 50 and    # 当前时间点首次冲入前50名
                  current_gthi_has_signal):      # 当前时间点带有"大于历史资金流入"标记

                score = self._calculate_comprehensive_score(current_main_inflow, current_gthi_days, '横空出世', stock_name, order_flow_tracker)

                # 创建基础信号
                signal = {
                    '信号类型': '横空出世',
                    '股票名称': stock_name,
                    '当前排名': current_rank,
                    '主力净流入': current_main_inflow,
                    '突破周期': current_gthi,
                    '综合评分': score,
                    '信号时间': current_time,
                    '代码': str(row.get('代码', '')) if pd.notna(row.get('代码', '')) else ''
                }

                # 添加主线强度评分
                if market_snapshot:
                    mainline_score, rating, reasons = self.mainline_scorer.score_signal(signal, market_snapshot)
                    signal['主线强度分'] = mainline_score
                    signal['评级'] = rating
                    signal['评分理由'] = ' | '.join(reasons) if reasons and isinstance(reasons, list) else (str(reasons) if reasons else '无相关主线')

                    # 【方案二】新主线候选的独立评级体系 - 二次校验逻辑
                    if signal['信号类型'] == '横空出世':
                        signal = self._apply_new_mainline_candidate_logic(signal, market_snapshot)

                    # 使用最终的评分结果进行日志打印
                    final_mainline_score = signal.get('主线强度分', mainline_score)
                    final_rating = signal.get('评级', rating)
                    print(f"   计算结果: 主线强度分={final_mainline_score}, 评级={final_rating}")
                else:
                    signal['主线强度分'] = 0
                    signal['评级'] = '★☆☆ [观察]'
                    signal['评分理由'] = '无市场数据'

                signals.append(signal)

            # 检测信号三："突破强化" (Breakthrough Reinforcement)
            elif (previous_rank is not None and  # 股票在上一个时间点存在记录
                  previous_rank <= 10 and  # 股票在上一个时间点的排名在前10名以内
                  previous_gthi_has_signal and  # 股票在上一个时间点已经带有"大于历史资金流入"的标记
                  current_rank <= 10 and  # 股票在当前时间点的排名仍然在前10名以内
                  current_gthi_has_signal and  # 股票在当前时间点仍然带有"大于历史资金流入"的标记
                  current_gthi_days > previous_gthi_days and  # 当前的历史突破天数大于上一个时间点的历史突破天数
                  current_main_inflow > previous_net_inflow * 1.1):  # 当前的主力净流入金额至少比上一个时间点增长10%

                score = self._calculate_comprehensive_score(current_main_inflow, current_gthi_days, '突破强化', stock_name, order_flow_tracker)

                # 创建基础信号
                signal = {
                    '信号类型': '突破强化',
                    '股票名称': stock_name,
                    '当前排名': current_rank,
                    '主力净流入': current_main_inflow,
                    '突破周期': current_gthi,
                    '综合评分': score,
                    '信号时间': current_time,
                    '代码': stock_flow_data.get('代码', '') if hasattr(stock_flow_data, 'get') else ''
                }
                
                # 添加主线强度评分
                if market_snapshot:
                    mainline_score, rating, reasons = self.mainline_scorer.score_signal(signal, market_snapshot)
                    signal['主线强度分'] = mainline_score
                    signal['评级'] = rating
                    signal['评分理由'] = ' | '.join(reasons) if reasons and isinstance(reasons, list) else (str(reasons) if reasons else '无相关主线')
                else:
                    signal['主线强度分'] = 0
                    signal['评级'] = '★☆☆ [观察]'
                    signal['评分理由'] = '无市场数据'
                    
                signals.append(signal)

        # 【【【反向惩罚机制：对没有大买盘支撑的信号进行惩罚】】】
        if order_flow_tracker:
            for signal in signals:
                stock_name = signal['股票名称']
                if stock_name not in order_flow_tracker or len(order_flow_tracker[stock_name]) == 0:
                    # 没有大买盘支撑，综合评分减少20%
                    original_score = signal['综合评分']
                    penalty_score = original_score * 0.8
                    signal['综合评分'] = penalty_score

                    # 更新评分理由
                    current_reason = signal.get('评分理由', '')
                    if current_reason and current_reason != '无相关主线':
                        signal['评分理由'] = current_reason + ' | 缺乏大买盘支撑(-20%)'
                    else:
                        signal['评分理由'] = '缺乏大买盘支撑(-20%)'

                    print(f"    【{stock_name}】缺乏大买盘支撑，评分惩罚: {original_score:.2f} → {penalty_score:.2f}")

        # 更新状态（为下一个时间点做准备）
        self.previous_stock_states = current_stock_states

        # 按主线强度分降序排序，其次按综合评分降序排序
        signals.sort(key=lambda x: (x.get('主线强度分', 0), x.get('综合评分', 0)), reverse=True)

        return signals

    def _apply_new_mainline_candidate_logic(self, signal, market_snapshot):
        """
        【方案二】新主线候选的独立评级体系 - 对"横空出世"信号进行二次校验

        参数:
        - signal: 原始信号字典
        - market_snapshot: 市场快照数据

        返回:
        - signal: 修改后的信号字典
        """
        stock_name = signal.get('股票名称', '')
        current_rank = signal.get('当前排名', 999)
        mainline_score = signal.get('主线强度分', 0)

        # 获取涨停股池数据
        limit_up_leaders = market_snapshot.get('limit_up_leaders', {})

        # 获取股票板块信息
        stock_code = signal.get('代码', '')
        sectors_info = get_stock_sectors(stock_name, stock_code)
        if sectors_info is None:
            sectors_info = {'concepts': [], 'industries': []}

        stock_sectors = sectors_info.get('concepts', []) + sectors_info.get('industries', [])

        # 规则A (强力型新主线)： 如果 个股资金流入排名 <= 5 并且 主线强度分 == 0
        if current_rank <= 5 and mainline_score == 0:
            signal['主线强度分'] = 15  # 给予高分
            signal['评级'] = '★★★ [新主线候选]'
            signal['评分理由'] = '资金总龙头奇袭非主线板块，极具新主线潜力'
            print(f"   【新主线候选-规则A】{stock_name}: 资金排名{current_rank}，主线强度分0 → 15")

        # 规则B (跟风型新主线)： 如果 个股资金流入排名 > 5 并且 主线强度分 == 0，但板块有>=3家涨停
        elif current_rank > 5 and mainline_score == 0:
            # 检查该股所属的概念/行业，在涨停股池中是否有>=3家涨停
            sector_with_enough_limit_ups = []

            for sector in stock_sectors:
                if sector in limit_up_leaders:
                    sector_info = limit_up_leaders[sector]
                    # 检查涨停数量（可能的字段名：count, limit_up_count, 涨停数等）
                    limit_up_count = 0
                    if isinstance(sector_info, dict):
                        limit_up_count = sector_info.get('count', 0) or sector_info.get('limit_up_count', 0) or sector_info.get('涨停数', 0)
                    elif isinstance(sector_info, (int, float)):
                        limit_up_count = int(sector_info)

                    if limit_up_count >= 3:
                        sector_with_enough_limit_ups.append(f"{sector}({limit_up_count}家)")

            if sector_with_enough_limit_ups:
                signal['主线强度分'] = 8  # 给予中等分数
                signal['评级'] = '★★☆ [板块效应驱动]'
                signal['评分理由'] = f'个股资金虽非顶流，但所属板块已形成集团冲锋: {", ".join(sector_with_enough_limit_ups)}'
                print(f"   【新主线候选-规则B】{stock_name}: 板块效应驱动，主线强度分0 → 8")

        return signal


def enable_volume_validation(enable=True, multiplier_threshold=1.5):
    """
    启用或禁用成交量验证功能
    
    参数:
    - enable: 是否启用成交量验证
    - multiplier_threshold: 成交量倍数阈值
    
    使用示例:
    # 启用成交量验证，要求成交量至少是之前的2倍
    enable_volume_validation(True, 2.0)
    
    # 禁用成交量验证
    enable_volume_validation(False)
    """
    global ENABLE_VOLUME_VALIDATION, VOLUME_MULTIPLIER_THRESHOLD
    ENABLE_VOLUME_VALIDATION = enable
    VOLUME_MULTIPLIER_THRESHOLD = multiplier_threshold
    
    status = "已启用" if enable else "已禁用"
    print(f"[配置更新] 成交量验证功能: {status}")
    if enable:
        print(f"[配置更新] 成交量倍数阈值: {multiplier_threshold}")


def add_volume_field_mapping(field_name, mapped_name='volume'):
    """
    添加新的成交量字段映射
    
    参数:
    - field_name: 数据源中的字段名
    - mapped_name: 映射到的标准字段名 ('volume' 或 'turnover')
    
    使用示例:
    # 添加新的成交量字段映射
    add_volume_field_mapping('实时成交量', 'volume')
    add_volume_field_mapping('实时换手率', 'turnover')
    """
    global VOLUME_FIELD_MAPPING
    VOLUME_FIELD_MAPPING[field_name] = mapped_name
    print(f"[配置更新] 添加成交量字段映射: '{field_name}' -> '{mapped_name}'")


def analyze_stock_flow_gap(df_stocks, current_time=None, data_dir=None, ignition_detector=None, file_format=None):
    """V5.1 个股资金流断层检测：基于动态阈值+持续性验证+成交量共振的日内自适应版 + 点火检测"""

    # 1. 数据预处理 - 使用格式感知的过滤策略
    if file_format:
        positive_flow_df = apply_stock_filter(df_stocks, file_format)
    else:
        # 兼容旧调用，使用默认过滤策略
        positive_flow_df = df_stocks[df_stocks['今日主力净流入-净额'] > 0]
        
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- 个股资金流数据不足，无法分析 ---】"

    # 2. V5.1 点火信号检测（使用传入的、持久化的检测器实例）
    if ignition_detector is None:
        # 如果外部没有提供检测器，创建一个临时的（不推荐，会丢失历史快照）
        ignition_detector = StockFlowIgnitionDetector()

    ignition_signals = ignition_detector.detect_ignition_signals(positive_flow_df, current_time)

    # 3. 原有的断层检测逻辑
    # 取前50名进行分析
    ANALYSIS_TOP_N_STOCKS = 50
    inflows = positive_flow_df.head(ANALYSIS_TOP_N_STOCKS)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2:
        return f"【--- 个股资金流数据不足，无法分析 ---】"

    names = positive_flow_df.head(ANALYSIS_TOP_N_STOCKS)['名称'].tolist()

    # 4. 竞争激烈度分析 - 优先检查
    competition_info = analyze_market_competition(inflows, names)

    # 5. 市场状态感知
    market_state = analyze_market_state(inflows)

    # 6. 全局断层点搜索
    gap_scores, max_gap = find_all_gap_points(inflows)

    # 7. 集团识别
    group_info = identify_leading_group(inflows, max_gap["position"], names)

    # 8. 动态阈值计算
    thresholds = calculate_dynamic_thresholds(market_state)

    # 9. 综合评判
    total_score, scores = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)

    # 10. 生成智能报告（包含点火信号）
    return generate_stock_flow_report_with_ignition(
        market_state, max_gap, group_info, thresholds, total_score, scores,
        competition_info, ignition_signals
    )


def generate_stock_flow_report(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None):
    """个股资金流智能报告生成器"""
    
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]
        
        return (f"【--- 个股资金流{competition_type}，无明显龙头 ---】\n"
                f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
    
    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]
    
    if not is_gap_found:
        # 未发现断层的报告
        leader_name = group_info["members"][0]
        return (f"【--- 个股资金流未发现显著资金断层 ---】\n"
                f"  当前龙头: 【{leader_name}】\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
    
    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "个股单龙头断层"
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】"
    else:
        gap_type = f"个股Top {group_info['size']} 领先集团"
        leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"
    
    # 断层分析描述
    gap_desc = (f"在第{max_gap['position']+1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap']/1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")
    
    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")
    
    report_lines = [
        f"【★★★★★ 个股资金流发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]
    
    return "\n".join(report_lines)


def generate_ignition_report(ignition_signals):
    """V7.0 - 生成主力信号报告（兼容“爆发点火”与“持续攻击”两种模型）"""
    if not ignition_signals:
        return ""

    # 分离不同类型的信号
    ignition_signals_list = [s for s in ignition_signals if '持续攻击' not in s.get('signal_type', '')]
    sustained_signals_list = [s for s in ignition_signals if '持续攻击' in s.get('signal_type', '')]

    report_lines = []

    # 报告“爆发点火”信号
    if ignition_signals_list:
        top_ignition = sorted(ignition_signals_list, key=lambda x: x.get('score', 0), reverse=True)[:3]
        report_lines.append("【🚀🚀🚀 主力“爆发点火”信号 (V5.1模型)! 🚀🚀🚀】")
        for i, signal in enumerate(top_ignition, 1):
            stock_name = signal['stock_name']
            stock_code = signal['stock_code']
            signal_type = signal['signal_type']
            time_str = signal['time'].strftime('%H:%M:%S') if hasattr(signal['time'], 'strftime') else str(
                signal['time'])

            report_lines.append(f"  信号定性: {signal_type}")
            report_lines.append(f"  点火个股: 【{stock_name} ({stock_code})】 at {time_str}")
            report_lines.append("")

            pf_percent = signal['pf'] * 100
            if 70 < pf_percent < 150:
                pf_status = "健康状态✓"
            elif pf_percent >= 150:
                pf_status = "出货风险⚠"
            else:
                pf_status = "意图不明❓"

            report_lines.append("  核心引爆数据 (持续验证后):")
            report_lines.append(f"  - 排名攻击区: 第{signal['new_rank']}名 (< 150名攻击区)")
            report_lines.append(
                f"  - 排名加速异常: WRA={signal['wra']:.3f} (超越阈值{signal.get('wra_exceed_ratio', 0):.1f}倍)")
            report_lines.append(
                f"  - 资金冲击力爆发: CT={signal['ct']:.0f}万元/分钟 (超越阈值{signal.get('ct_exceed_ratio', 0):.1f}倍)")
            report_lines.append(f"  - 主力纯度: PF={pf_percent:.1f}% ({pf_status})")
            report_lines.append(f"  - 价格响应: 股价瞬间拉升 {signal.get('price_response', 0.0):.2f}%")
            report_lines.append("")
            report_lines.append("  综合评估:")
            report_lines.append(f"  - 点火强度分: {signal.get('score', 0.0):.1f}/10 分")
            master_insight = generate_v51_master_insight(signal)
            report_lines.append(f"  - 大师解读: {master_insight}")

            if i < len(top_ignition):
                report_lines.append("\n" + "-" * 50)

    # 报告“持续攻击”信号
    if sustained_signals_list:
        if report_lines: report_lines.append("\n\n")  # 添加分隔
        top_sustained = sorted(sustained_signals_list, key=lambda x: x.get('score', 0), reverse=True)[:3]
        report_lines.append("【📈📈📈 主力“持续攻击”信号 (V7.0模型)! 📈📈📈】")
        for i, signal in enumerate(top_sustained, 1):
            stock_name = signal['stock_name']
            stock_code = signal['stock_code']

            report_lines.append(f"  信号定性: {signal['signal_type']}")
            report_lines.append(f"  攻击个股: 【{stock_name} ({stock_code})】")
            report_lines.append("")
            report_lines.append(f"  核心追踪数据 (在 {signal['duration_minutes']} 分钟内):")
            report_lines.append(
                f"  - 排名趋势: 从 {signal['start_rank']} 名稳步提升至 {signal['end_rank']} 名 (趋势斜率: {signal['rank_slope']:.2f})")
            report_lines.append(f"  - 资金持续净增: {format_amount(signal['cumulative_inflow'])}")
            report_lines.append(f"  - 攻击稳定性: {signal['inflow_stability']:.1%} 的时间点呈净流入")
            report_lines.append(f"  - 当前涨跌幅: {signal.get('price_response', 0.0):.2f}%")
            report_lines.append("")
            report_lines.append("  综合评估:")
            report_lines.append(f"  - 攻击强度分: {signal.get('score', 0.0):.1f}/10 分")
            master_insight = generate_v51_master_insight(signal)
            report_lines.append(f"  - 大师解读: {master_insight}")

            if i < len(top_sustained):
                report_lines.append("\n" + "-" * 50)

    return "\n".join(report_lines)


def generate_master_insight(signal):
    """生成大师解读（保留原版本兼容性）"""
    score = signal['score']
    wra = signal['wra']
    ct = signal['ct']
    rank_jump = signal['rank_jump']

    if score >= 9:
        return f"超强主力重拳出击，排名暴涨{rank_jump}位，后续爆发力值得重点关注！"
    elif score >= 7:
        return f"明显资金集中流入，{ct:.0f}万资金快速推升，短期有望持续强势。"
    elif score >= 5:
        return f"资金开始聚集，排名提升{rank_jump}位，可关注后续资金接力情况。"
    else:
        return f"初现资金流入迹象，需观察是否有后续资金跟进。"


def generate_v51_master_insight(signal):
    """V7.0 - “爆发点火”与“持续攻击”双模型大师解读"""
    score = signal.get('score', 0)
    signal_type = signal.get('signal_type', '')

    # 新增：为“持续攻击”信号生成解读
    if '持续攻击' in signal_type:
        cumulative_inflow_str = format_amount(signal.get('cumulative_inflow', 0))
        duration_minutes = signal.get('duration_minutes', 'N/A')
        start_rank = signal.get('start_rank', 'N/A')
        end_rank = signal.get('end_rank', 'N/A')

        if score >= 8.5:
            return f"主力耐心布局的典范！在长达{duration_minutes}分钟内持续吸筹超{cumulative_inflow_str}，排名从{start_rank}名稳步攻入{end_rank}名，后续有望转入主升浪！"
        elif score >= 7.5:
            return f"趋势的力量！资金在{duration_minutes}分钟内稳定流入{cumulative_inflow_str}，排名趋势明确，主力控盘迹象明显，值得高度关注。"
        else:
            return f"水滴石穿！虽然单次攻击不强，但在{duration_minutes}分钟内持续不断的资金推动，已形成良好上升趋势，建议放入观察池。"

    # 保留原有的“爆发点火”信号解读
    wra_exceed = signal.get('wra_exceed_ratio', 1)
    ct_exceed = signal.get('ct_exceed_ratio', 1)
    pf = signal.get('pf', 0)
    current_rank = signal.get('new_rank', 999)
    market_data_points = signal.get('market_data_points', 0)

    signal_time = signal.get('time', None)
    hour = signal_time.hour if signal_time and hasattr(signal_time, 'hour') else 10
    minute = signal_time.minute if signal_time and hasattr(signal_time, 'minute') else 30

    pf_percent = pf * 100
    is_healthy_pf = 70 < pf_percent < 150
    is_risk_pf = pf_percent >= 150

    is_wra_dominant = wra_exceed > ct_exceed and wra_exceed > 1.5
    is_ct_dominant = ct_exceed > wra_exceed and ct_exceed > 1.5
    is_opening_period = hour == 9 and minute <= 45
    is_closing_period = hour >= 14 and minute >= 30

    if score >= 9.5:
        if is_risk_pf:
            return f"🚨 超强信号但需警惕！经4重验证确认的顶级点火信号，但PF={pf_percent:.0f}%超过150%存在出货风险，建议短线操作！"
        elif is_wra_dominant and is_healthy_pf:
            return f"🎯 完美四重验证！持续性+成交量+技巧型攻击三重共振，PF={pf_percent:.0f}%处于健康区间，排名效率超越{wra_exceed:.1f}倍，堪称教科书级操作！"
        elif is_ct_dominant and is_healthy_pf:
            return f"💎 钻石级信号！经严格四重验证的暴力美学，资金冲击力超越{ct_exceed:.1f}倍，PF={pf_percent:.0f}%健康状态，主力决心可见一斑！"
        else:
            return f"🚀 究极进化！四重验证筛选的超级信号，经过持续性观察和成交量共振确认，这是万里挑一的精品！"
    elif score >= 8.5:
        if is_opening_period and is_healthy_pf:
            return f"⚡ 开盘即巅峰！经过严格验证的开盘强攻信号，PF={pf_percent:.0f}%健康区间+持续性确认，有望领跑全天！"
        elif is_closing_period and is_healthy_pf:
            return f"🔥 收官之作！尾盘经四重验证的精品信号，PF={pf_percent:.0f}%健康状态显示主力布局充分，明日值得期待！"
        elif is_risk_pf:
            return f"⚠️ 强势但有风险！虽然信号强度很高，但PF={pf_percent:.0f}%超标，可能存在边拉边出的情况，谨慎参与！"
        else:
            return f"⭐ 四重认证精品！持续性+成交量+动态阈值+绝对能量四重验证通过，PF={pf_percent:.0f}%状态良好，值得重点关注！"
    elif score >= 7.5:
        if is_healthy_pf:
            return f"💪 稳健四重验证！经过完整观察期筛选，PF={pf_percent:.0f}%处于健康区间，虽非顶级但胜在稳健可靠！"
        elif is_risk_pf:
            return f"📊 强信号谨慎参与！四重验证显示实力不俗，但PF={pf_percent:.0f}%偏高需警惕出货，适合短线高手！"
        else:
            return f"🎪 技巧型突破！经四重验证的均衡发力，基于{market_data_points}个样本的动态分析显示潜力可期！"
    elif score >= 7.0:
        if is_healthy_pf:
            return f"🌟 品质保证！四重验证机制筛选出的优质信号，PF={pf_percent:.0f}%健康状态+持续性确认，稳中求进的好选择！"
        else:
            return f"📈 严格筛选认证！经过持续性观察池和成交量验证，虽刚过门槛但质量有保障，值得持续观察！"
    else:
        return f"🔍 四重筛选入围！虽然刚刚通过严格验证，但在当前市场环境下已属精品，建议关注后续表现。"

def generate_v5_master_insight(signal):
    """V5.0 增强版大师解读 - 基于核心驱动因子的精细化分析"""
    score = signal['score']
    wra_exceed = signal.get('wra_exceed_ratio', 1)
    ct_exceed = signal.get('ct_exceed_ratio', 1)
    pf = signal['pf']
    current_rank = signal['new_rank']
    market_data_points = signal.get('market_data_points', 0)

    # 获取时间信息用于时段分析
    signal_time = signal.get('time', None)
    hour = signal_time.hour if signal_time and hasattr(signal_time, 'hour') else 10
    minute = signal_time.minute if signal_time and hasattr(signal_time, 'minute') else 30

    # 分析核心驱动因子特征
    is_wra_dominant = wra_exceed > ct_exceed and wra_exceed > 1.5  # WRA主导型
    is_ct_dominant = ct_exceed > wra_exceed and ct_exceed > 1.5    # CT主导型
    is_high_pf = pf > 0.8  # 高主力纯度
    is_opening_period = hour == 9 and minute <= 45  # 开盘时段
    is_closing_period = hour >= 14 and minute >= 30  # 尾盘时段

    # 基于驱动因子的精细化解读
    if score >= 9.5:
        if is_wra_dominant and not is_ct_dominant:
            return f"🎯 奇袭！资金量适中但排名提升效率极高(超越{wra_exceed:.1f}倍)，属四两拨千斤的技巧型攻击，主力操盘手段老练！"
        elif is_ct_dominant and is_high_pf:
            return f"� 王炸！绝对的资金碾压(超越{ct_exceed:.1f}倍)，主力纯度{pf*100:.0f}%不计成本强行拉升，市场分歧可能较大但态度坚决！"
        else:
            return f"�🚀 极致暴力美学！WRA超越市场95%门槛{wra_exceed:.1f}倍，CT冲击力达到前5%水平的{ct_exceed:.1f}倍，这是加特林机枪级别的火力覆盖！"

    elif score >= 8.5:
        if is_opening_period:
            return f"⚡ 闪电战！开盘即发动总攻(WRA超越{wra_exceed:.1f}倍)，意图争夺日内龙头地位，需关注板块承接力！"
        elif is_closing_period:
            return f"🔥 收官之战！尾盘强势拉升，主力纯度{pf*100:.0f}%显示决心坚定，明日高开概率较大！"
        else:
            return f"⚡ 精英中的精英！排名加速度不仅进入全市场前5%，还高出门槛{(wra_exceed-1)*100:.0f}%，主力纯度{pf*100:.0f}%显示血统纯正！"

    elif score >= 7.5:
        if is_wra_dominant:
            return f"🎪 技巧流！排名攻击效率超越{wra_exceed:.1f}倍，以巧制胜，主力可能在测试市场承接力！"
        elif is_ct_dominant:
            return f"🎯 暴力美学显现！资金攻击加速度达到市场前5%门槛的{ct_exceed:.1f}倍，当别人用步枪点射时，这里已是重机枪咆哮！"
        else:
            return f"💪 均衡发力！WRA和CT双重突破，主力攻击节奏稳健，后续持续性值得期待！"

    elif score >= 7.0:
        if is_opening_period:
            return f"🌅 晨光乍现！开盘时段即显露锋芒，基于{market_data_points}个样本的动态分析显示潜力巨大！"
        else:
            return f"💪 主力意图明确！基于{market_data_points}个市场样本的动态分析显示，该股已突破95百分位防线，值得重点关注！"

    else:
        return f"📊 动态阈值触发！虽然刚刚突破动态门槛，但在当前市场环境下已属异常，建议观察后续发展。"


def generate_stock_flow_report_with_ignition(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None, ignition_signals=None):
    """个股资金流智能报告生成器（包含点火信号）"""

    # 1. 首先显示点火信号（如果有）
    ignition_report = ""
    if ignition_signals:
        ignition_report = generate_ignition_report(ignition_signals) + "\n\n"

    # 2. 原有的断层检测报告
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]

        gap_report = (f"【--- 个股资金流{competition_type}，无明显龙头 ---】\n"
                     f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                     f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                     f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
        return ignition_report + gap_report

    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]

    if not is_gap_found:
        # 未发现断层的报告
        leader_name = group_info["members"][0]
        gap_report = (f"【--- 个股资金流未发现显著资金断层 ---】\n"
                     f"  当前龙头: 【{leader_name}】\n"
                     f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                     f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
        return ignition_report + gap_report

    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "个股单龙头断层"
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】"
    else:
        gap_type = f"个股Top {group_info['size']} 领先集团"
        leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"

    # 断层分析描述
    gap_desc = (f"在第{max_gap['position']+1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap']/1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")

    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")

    gap_report_lines = [
        f"【★★★★★ 个股资金流发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]

    gap_report = "\n".join(gap_report_lines)
    return ignition_report + gap_report


def analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type, sector_total_amount,
                                 mover_stocks=None, stock_flow_data=None, ignition_detector=None):
    """分析断层龙头板块的内部个股资金流入情况，并触发协同警报（V7.4 静默修复版）"""

    if sector_type != '行业' and sector_type != '概念':
        return f"  \n--- {leader_name}内部个股分析 ---\n  当前类型 {sector_type} 内部数据暂不可用，功能开发中..."

    # 根据板块类型选择相应的文件查找函数
    if sector_type == '概念':
        sector_file = find_concept_summary_file(leader_name, current_time, data_dir)
        if not sector_file:
            return f"  \n--- {leader_name}内部个股分析 ---\n  未找到{leader_name}概念文件"
    else:  # 行业
        sector_file = find_sector_summary_file(leader_name, current_time, data_dir)
        if not sector_file:
            return f"  \n--- {leader_name}内部个股分析 ---\n  未找到对应的内部资金流数据文件"

    df_internal = parse_sector_internal_data(os.path.join(data_dir, sector_file))
    if df_internal is None or df_internal.empty:
        return f"  \n--- {leader_name}内部个股分析 ---\n  无正流入个股数据"

    # V7.4 修复：更健壮的协同信号检测，静默处理错误
    if (mover_stocks and not df_internal.empty and stock_flow_data is not None and 
        ignition_detector and ignition_detector.previous_snapshot):
        
        try:
            previous_stock_snapshot = ignition_detector.previous_snapshot
            mover_stocks_set = set(mover_stocks)
            sector_stock_list = df_internal['名称'].tolist()
            candidate_stocks = [stock for stock in sector_stock_list if stock in mover_stocks_set]
            
            # 只有找到协同候选股票时才进行检测
            if candidate_stocks:
                for stock_name in candidate_stocks:
                    try:
                        # 检查股票是否在整体资金流数据中
                        # 修复pandas Series布尔值歧义问题
                        name_mask = stock_flow_data['名称'] == stock_name
                        current_overall_stock_info_list = stock_flow_data.loc[name_mask]
                        if current_overall_stock_info_list.empty:
                            continue
                        
                        # 检查是否有历史数据
                        if stock_name not in previous_stock_snapshot:
                            continue
                        
                        current_overall_stock_info = current_overall_stock_info_list.iloc[0]
                        current_inflow = current_overall_stock_info['今日主力净流入-净额']
                        current_rank = current_overall_stock_info_list.index[0] + 1
                        
                        previous_stock_data = previous_stock_snapshot[stock_name]
                        previous_inflow = previous_stock_data.get('net_inflow', 0)
                        previous_rank = previous_stock_data.get('rank', 9999)
                        
                        inflow_delta = current_inflow - previous_inflow
                        rank_jump = previous_rank - current_rank
                        
                        # 检查是否满足协同条件
                        is_significant_move = False
                        reasons = []
                        
                        if inflow_delta >= MIN_SYNERGY_INFLOW_DELTA:
                            is_significant_move = True
                            reasons.append(f"资金猛增 {format_amount(inflow_delta)}")
                        
                        if rank_jump >= MIN_SYNERGY_RANK_JUMP:
                            is_significant_move = True
                            reasons.append(f"排名跃升 {rank_jump} 位 (从{previous_rank}名到{current_rank}名)")
                        
                        if is_significant_move:
                            reason_str = ", ".join(reasons)
                            generate_synergy_alert(stock_name, leader_name, current_time, reason_str)
                            
                    except Exception:
                        # 静默处理个股级别的异常
                        continue
        except Exception:
            # 静默处理整个协同检测的异常
            pass

    return generate_internal_analysis_report(df_internal, leader_name, sector_file, sector_total_amount, current_time)


def generate_internal_analysis_report(df_internal, sector_name, sector_file, sector_total_amount, current_time):
    """生成板块内部个股资金流分析报告（支持双龙头检测和占比判断）"""
    try:
        # 取前10名用于分析
        top_stocks = df_internal.head(10)

        if len(top_stocks) < 2:
            return f"  \n--- {sector_name}内部个股分析 ---\n  数据不足，仅有{len(top_stocks)}只正流入个股"

        # 计算内部断层 - 增强版（支持双龙头检测+占比判断）
        inflows = top_stocks['今日主力净流入-净额'].tolist()
        names = top_stocks['名称'].tolist()

        # 尝试获取股票代码（如果数据中包含）
        codes = []
        if '代码' in top_stocks.columns:
            codes = top_stocks['代码'].tolist()
        else:
            # 如果没有代码列，使用硬编码映射
            stock_name_to_code = {
                '科大讯飞': '002230',
                '寒武纪-U': '688256',
                '兆易创新': '603986',
                '阿石创': '300706',
                '宁波银行': '002142',
                '招商银行': '600036',
                '亚太药业': '002370',
                '康泰医学': '300869',
                '延华智能': '002178',
                '蓝色光标': '300058',
                '岩山科技': '301555',
                '拓维信息': '002261'
            }
            codes = [stock_name_to_code.get(name, '000000') for name in names]
        
        # 基础比例计算
        first_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
        first_amount = inflows[0] / 1e8  # 第1名资金量（亿）
        second_amount = inflows[1] / 1e8  # 第2名资金量（亿）
        first_second_gap = (inflows[0] - inflows[1]) / 1e8  # 第1-2名差距（亿）
        
        # 【新增】占比判断逻辑
        first_ratio_sector = (first_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        second_ratio_sector = (second_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        dual_ratio_sector = first_ratio_sector + second_ratio_sector
        
        # 如果有第3名，计算第2-3名关系
        has_third = len(inflows) >= 3 and inflows[2] > 0
        if has_third:
            second_third_ratio = inflows[1] / inflows[2]
            third_amount = inflows[2] / 1e8
            second_third_gap = (inflows[1] - inflows[2]) / 1e8
            third_ratio_sector = (third_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        else:
            second_third_ratio = float('inf')
            third_amount = 0
            second_third_gap = 0
            third_ratio_sector = 0
        
        # 【优先级1】基于板块占比的绝对龙头判断
        is_absolute_leader = False
        absolute_leader_reason = ""
        
        # 更严格的绝对龙头标准
        if first_ratio_sector >= 40.0:  # 第1名占板块40%以上，绝对龙头
            is_absolute_leader = True
            absolute_leader_reason = f"板块占比极高({first_ratio_sector:.1f}%)，绝对龙头地位"
        elif first_ratio_sector >= 35.0 and first_ratio >= 1.25:  # 35%以上且明显优势
            is_absolute_leader = True
            absolute_leader_reason = f"板块占比很高({first_ratio_sector:.1f}%)且明显领先({first_ratio:.2f}倍)"
        
        # 【优先级2】基于数据分布的智能多龙头检测
        is_dual_leader = False
        is_triple_leader = False
        dual_leader_reason = ""
        triple_leader_reason = ""
        
        if not is_absolute_leader and has_third:
            # 动态分析数据分布特征
            top5_amounts = [x/1e8 for x in inflows[:min(5, len(inflows))] if x > 0]
            
            if len(top5_amounts) >= 3:
                import numpy as np
                
                # 计算分布特征
                amounts_array = np.array(top5_amounts)
                cv = np.std(amounts_array) / np.mean(amounts_array) if np.mean(amounts_array) > 0 else 0  # 变异系数
                
                # 计算梯度下降率
                gaps = [top5_amounts[i] / top5_amounts[i+1] for i in range(len(top5_amounts)-1)]
                first_gap = gaps[0] if len(gaps) > 0 else 1.0  # 第1-2名差距
                second_gap = gaps[1] if len(gaps) > 1 else 1.0  # 第2-3名差距
                third_gap = gaps[2] if len(gaps) > 2 else 1.0   # 第3-4名差距
                
                # 计算前三名占比
                third_ratio_sector = (top5_amounts[2] / sector_total_amount) * 100 if sector_total_amount > 0 else 0
                triple_ratio_sector = first_ratio_sector + second_ratio_sector + third_ratio_sector
                
                # 【优先检测】三足鼎立：前三名都很接近，且与第4名有差距
                triple_close = (first_gap < 1.35 and second_gap < 1.15)  # 前三名都很接近
                triple_dominance = triple_ratio_sector >= 50.0  # 前三名合计占比足够高
                third_significant = third_ratio_sector >= 12.0  # 第3名占比不能太小
                clear_separation_from_fourth = third_gap >= 1.20  # 与第4名有明显差距
                
                if (triple_close and triple_dominance and third_significant and 
                    (clear_separation_from_fourth or triple_ratio_sector >= 55.0)):
                    is_triple_leader = True
                    triple_leader_reason = (f"三足鼎立：前三名合计占比{triple_ratio_sector:.1f}%，"
                                          f"个别占比{first_ratio_sector:.1f}%、{second_ratio_sector:.1f}%、{third_ratio_sector:.1f}%，"
                                          f"内部接近(最大差距{max(first_gap, second_gap):.2f}倍)")
                
                # 【次要检测】双龙头：仅在非三足鼎立时检测
                elif not is_triple_leader:
                    # 双龙头检测条件：基于数据分布和相对差距
                    # 条件1：前两名合计占比足够高
                    dual_dominance = dual_ratio_sector >= 35.0
                    
                    # 条件2：个股占比平衡（避免一强一弱）
                    balanced_shares = (first_ratio_sector >= 15.0 and 
                                     second_ratio_sector >= 12.0 and
                                     first_ratio_sector < 35.0)
                    
                    # 条件3：前两名相对接近
                    close_leaders = first_gap < 1.40
                    
                    # 条件4：智能断层检测（基于分布特征）
                    # 如果数据分布较为均匀（CV较小），则要求更明显的断层
                    # 如果数据分布不均匀（CV较大），则可以接受较小的断层
                    if cv < 0.4:  # 分布相对均匀，要求明显断层
                        clear_separation = second_gap >= 1.20  # 提高要求到1.20
                    elif cv < 0.6:  # 分布中等，中等要求
                        clear_separation = second_gap >= 1.15  # 提高要求到1.15
                    else:  # 分布不均匀，降低要求
                        clear_separation = second_gap >= 1.10
                    
                    # 条件5：占比补偿机制（更严格）
                    # 如果前两名合计占比很高，可以适当放宽断层要求
                    if dual_ratio_sector >= 50.0:  # 提高到50%
                        ratio_compensation = second_gap >= 1.05
                    else:
                        ratio_compensation = False
                    
                    # 综合判断
                    if (dual_dominance and balanced_shares and close_leaders and 
                        (clear_separation or ratio_compensation)):
                        is_dual_leader = True
                        
                        if ratio_compensation and not clear_separation:
                            dual_leader_reason = (f"高占比双龙头：合计占比{dual_ratio_sector:.1f}%，"
                                                f"分布CV{cv:.2f}，第2-3名差距{second_gap:.2f}倍")
                        else:
                            dual_leader_reason = (f"标准双龙头：合计占比{dual_ratio_sector:.1f}%，"
                                                f"分布CV{cv:.2f}，明显领先第3名({second_gap:.2f}倍)")
        
        # 【优先级3】动态阈值版单龙头检测（原有逻辑，仅在无占比优势时使用）
        is_single_leader = False
        single_leader_reason = ""
        
        if not is_absolute_leader and not is_dual_leader:
            # 计算动态阈值 - 基于前10名数据
            top10_amounts = [x/1e8 for x in inflows[:min(10, len(inflows))] if x > 0]
            
            if len(top10_amounts) >= 3:
                # 策略1: 基于平均值的60%作为双龙头最低门槛
                avg_amount = sum(top10_amounts) / len(top10_amounts)
                min_leader_threshold = avg_amount * 0.6
                
                # 策略3: 动态绝对差距阈值（基于前10名标准差）
                import numpy as np
                std_amount = np.std(top10_amounts) if len(top10_amounts) > 1 else 0
                min_gap_threshold = max(0.1, std_amount * 0.3)  # 最小0.1亿
                
                # 单龙头条件（动态版）：
                # 条件1: 相对优势显著 (1.25倍以上)
                if first_ratio >= 1.25:
                    is_single_leader = True
                    single_leader_reason = f"相对优势显著({first_ratio:.2f}倍)"
                
                # 条件2: 绝对差距较大 (≥动态阈值) 且有一定相对优势 (≥1.1倍)
                elif first_second_gap >= min_gap_threshold and first_ratio >= 1.1:
                    is_single_leader = True
                    single_leader_reason = f"绝对差距较大({first_second_gap:.2f}亿)"
                
                # 条件3: 中等资金且有明显优势 (≥动态阈值 且 ≥1.15倍)
                elif first_amount >= min_leader_threshold and first_ratio >= 1.15:
                    is_single_leader = True
                    single_leader_reason = f"资金量优势({first_amount:.2f}亿，领先{first_second_gap:.2f}亿)"
            else:
                # 如果数据不足，降级为简单判断
                if first_ratio >= 1.2 and first_second_gap >= 0.1:
                    is_single_leader = True
                    single_leader_reason = f"数据有限情况下的单龙头判断({first_ratio:.2f}倍)"
        
        # 构建报告
        report_lines = [f"  \n--- {sector_name}板块内部个股资金流入 Top 10 (模拟时间点: {current_time}) ---"]
        report_lines.append(f"  板块总资金: {sector_total_amount:.2f}亿")
        
        if is_absolute_leader:
            report_lines.append(f"  【★★★ {sector_name}板块内部绝对龙头! ★★★】")
            report_lines.append(f"  内部格局: 绝对龙头")
            report_lines.append(f"  绝对龙头: 【{names[0]}】({codes[0]})")
            report_lines.append(f"  占比优势: {first_ratio_sector:.1f}% (个股资金{first_amount:.2f}亿)")
            report_lines.append(f"  相对优势: {first_ratio:.2f}倍，绝对差距{first_second_gap:.2f}亿")
            report_lines.append(f"  判断依据: {absolute_leader_reason}")
        elif is_triple_leader:
            report_lines.append(f"  【★★★ {sector_name}板块内部三足鼎立格局! ★★★】")
            report_lines.append(f"  内部格局: 三足鼎立")
            report_lines.append(f"  三强集团: 【{names[0]}】({codes[0]})、【{names[1]}】({codes[1]})、【{names[2]}】({codes[2]})")
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% + {second_ratio_sector:.1f}% + {third_ratio_sector:.1f}% = {triple_ratio_sector:.1f}%")
            report_lines.append(f"  资金分析: {first_amount:.2f}亿、{second_amount:.2f}亿、{third_amount:.2f}亿")
            report_lines.append(f"  内部差距: 第1-2名{first_ratio:.2f}倍，第2-3名{second_third_ratio:.2f}倍")
            report_lines.append(f"  判断依据: {triple_leader_reason}")
        elif is_dual_leader:
            report_lines.append(f"  【★★ {sector_name}板块内部发现双龙头格局! ★★】")
            report_lines.append(f"  内部格局: 双龙头领先")
            report_lines.append(f"  双龙头: 【{names[0]}】({codes[0]})& 【{names[1]}】({codes[1]})")
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% + {second_ratio_sector:.1f}% = {dual_ratio_sector:.1f}%")
            report_lines.append(f"  资金分析: {first_amount:.2f}亿 vs {second_amount:.2f}亿 (相差{first_ratio:.2f}倍)")
            if has_third:
                report_lines.append(f"  领先优势: 前二名vs第3名 {second_third_ratio:.2f}倍，断层{second_third_gap:.2f}亿")
            report_lines.append(f"  判断依据: {dual_leader_reason}")
        elif is_single_leader:
            report_lines.append(f"  【★ {sector_name}板块内部发现个股龙头! ★】")
            report_lines.append(f"  内部格局: 单股龙头")
            report_lines.append(f"  个股龙头: 【{names[0]}】({codes[0]})")
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% (个股资金{first_amount:.2f}亿)")
            report_lines.append(f"  领先优势: {first_ratio:.2f}倍，绝对差距{first_second_gap:.2f}亿")
            report_lines.append(f"  判断依据: {single_leader_reason}")
        else:
            report_lines.append(f"  【{sector_name}板块内部竞争激烈】")
            report_lines.append(f"  占比分析: 第1名{first_ratio_sector:.1f}%，第2名{second_ratio_sector:.1f}%")
            report_lines.append(f"  第1名优势: {first_ratio:.2f}倍，差距{first_second_gap:.2f}亿 (无明显龙头)")
        
        # 检查历史最大值并生成表格数据
        # 添加历史最大值检查
        top_stocks_with_hist = check_historical_max_inflow(top_stocks.copy(), BACKTEST_DATE)

        table_data = []
        for i, (_, row) in enumerate(top_stocks_with_hist.iterrows(), 1):
            amount_yi = row['今日主力净流入-净额'] / 1e8
            hist_max_flag = row.get('大于历史资金流入', '')
            table_data.append([i, row['名称'], f"{amount_yi:.2f}亿", hist_max_flag])

        table_str = tabulate(table_data, headers=['排名', '股票名称', '主力净流入', '大于历史资金流入'],
                           tablefmt='psql', showindex=False)
        report_lines.append(table_str)
        
        # 添加数据源信息
        report_lines.append(f"  数据源: {sector_file}")
        
        return "\n".join(report_lines)
        
    except Exception as e:
        return f"  \n--- {sector_name}内部个股分析 ---\n  生成报告失败: {e}"





def _generate_tiered_report(stock_synergy_scores, main_battlefield_names, potential_battlefield_names, min_synergy_score):
    """
    【大师级决策模块辅助函数】智能分层归类并生成大师级报告
    返回: (tier1_stocks, tier2_stocks, tier3_stocks, report_content)
    """
    # === 第四步：智能分层归类 ===
    tier1_stocks = []  # 第一梯队：龙头中的龙头
    tier2_stocks = []  # 第二梯队：板块内中军
    tier3_stocks = []  # 第三梯队：独立趋势龙头

    for stock in stock_synergy_scores:
        # 过滤低分股票
        if stock['total_score'] < min_synergy_score:
            continue

        # 第一梯队：主战场 + 涨停 + 资金排名靠前
        if (stock['is_main_battlefield'] and
            stock['is_limit_up'] and
            stock['rank'] <= 20):
            stock['tier'] = '第一梯队'
            stock['description'] = '龙头中的龙头'
            tier1_stocks.append(stock)

        # 第三梯队：非主战场/潜在战场但资金排名极高
        elif (not stock['is_main_battlefield'] and
              not stock['is_potential_battlefield'] and
              stock['rank'] <= 5):
            stock['tier'] = '第三梯队'
            stock['description'] = '独立趋势龙头'
            tier3_stocks.append(stock)

        # 第二梯队：主战场或潜在战场的其他优质股票
        elif (stock['is_main_battlefield'] or stock['is_potential_battlefield']):
            stock['tier'] = '第二梯队'
            stock['description'] = '板块内中军'
            tier2_stocks.append(stock)

    # 按联动分排序
    tier1_stocks.sort(key=lambda x: x['total_score'], reverse=True)
    tier2_stocks.sort(key=lambda x: x['total_score'], reverse=True)
    tier3_stocks.sort(key=lambda x: x['total_score'], reverse=True)

    # === 第五步：生成大师级报告 ===
    report_lines = []
    report_lines.append(f"\n🏆 【核心股票池三梯队分析】(联动分≥{min_synergy_score}分)")
    report_lines.append("=" * 80)

    print(f"\n🏆 【核心股票池三梯队分析】(联动分≥{min_synergy_score}分)")
    print("=" * 80)

    # 第一梯队报告
    if tier1_stocks:
        tier1_header = f"\n🥇 第一梯队：龙头中的龙头 ({len(tier1_stocks)}只)"
        tier1_desc = "   特征：主战场 + 涨停形态 + 资金排名靠前"

        report_lines.append(tier1_header)
        report_lines.append(tier1_desc)
        print(tier1_header)
        print(tier1_desc)

        tier1_table = []
        for i, stock in enumerate(tier1_stocks, 1):
            tier1_table.append([
                i,
                stock['name'],
                f"{stock['total_score']:.2f}",
                stock['rank'],
                stock['industry_display'],  # 显示行业
                stock['concept_display'],   # 显示概念
                "✅" if stock['is_limit_up'] else "",
                format_amount(stock['inflow']),
                stock['description']
            ])

        tier1_table_str = tabulate(tier1_table,
                      headers=['优先级', '股票名称', '联动分', '资金排名', '所属行业', '所属概念', '涨停', '净流入', '决策解读'],
                      tablefmt='psql', showindex=False)
        report_lines.append(tier1_table_str)
        print(tier1_table_str)
    else:
        tier1_empty = f"\n🥇 第一梯队：龙头中的龙头 (0只)"
        tier1_empty_desc = "   当前市场暂无同时满足'主战场+涨停+资金强势'的核心龙头"

        report_lines.append(tier1_empty)
        report_lines.append(tier1_empty_desc)
        print(tier1_empty)
        print(tier1_empty_desc)

    # 第二梯队报告
    if tier2_stocks:
        tier2_header = f"\n🥈 第二梯队：板块内中军 ({len(tier2_stocks)}只)"
        tier2_desc = "   特征：主战场或潜在战场内的优质补涨标的"

        report_lines.append(tier2_header)
        report_lines.append(tier2_desc)
        print(tier2_header)
        print(tier2_desc)

        tier2_table = []
        for i, stock in enumerate(tier2_stocks[:10], 1):  # 最多显示10只
            tier2_table.append([
                i,
                stock['name'],
                f"{stock['total_score']:.2f}",
                stock['rank'],
                stock['industry_display'],  # 显示行业
                stock['concept_display'],   # 显示概念
                "✅" if stock['is_limit_up'] else "",
                format_amount(stock['inflow']),
                stock['description']
            ])

        tier2_table_str = tabulate(tier2_table,
                      headers=['优先级', '股票名称', '联动分', '资金排名', '所属行业', '所属概念', '涨停', '净流入', '决策解读'],
                      tablefmt='psql', showindex=False)
        report_lines.append(tier2_table_str)
        print(tier2_table_str)
    else:
        tier2_empty = f"\n🥈 第二梯队：板块内中军 (0只)"
        tier2_empty_desc = "   当前主战场和潜在战场内暂无符合条件的中军股票"

        report_lines.append(tier2_empty)
        report_lines.append(tier2_empty_desc)
        print(tier2_empty)
        print(tier2_empty_desc)

    # 第三梯队报告
    if tier3_stocks:
        tier3_header = f"\n🥉 第三梯队：独立趋势龙头 ({len(tier3_stocks)}只)"
        tier3_desc = "   特征：非热点板块但资金排名极高的独立强势股"

        report_lines.append(tier3_header)
        report_lines.append(tier3_desc)
        print(tier3_header)
        print(tier3_desc)

        tier3_table = []
        for i, stock in enumerate(tier3_stocks, 1):
            tier3_table.append([
                i,
                stock['name'],
                f"{stock['total_score']:.2f}",
                stock['rank'],
                stock['industry_display'],  # 显示行业
                stock['concept_display'],   # 显示概念
                "✅" if stock['is_limit_up'] else "",
                format_amount(stock['inflow']),
                stock['description']
            ])

        tier3_table_str = tabulate(tier3_table,
                      headers=['优先级', '股票名称', '联动分', '资金排名', '所属行业', '所属概念', '涨停', '净流入', '决策解读'],
                      tablefmt='psql', showindex=False)
        report_lines.append(tier3_table_str)
        print(tier3_table_str)
    else:
        tier3_empty = f"\n🥉 第三梯队：独立趋势龙头 (0只)"
        tier3_empty_desc = "   当前市场暂无资金排名极高的独立强势股"

        report_lines.append(tier3_empty)
        report_lines.append(tier3_empty_desc)
        print(tier3_empty)
        print(tier3_empty_desc)

    # 解读说明
    total_qualified = len(tier1_stocks) + len(tier2_stocks) + len(tier3_stocks)
    explanation_lines = [
        f"\n📊 【联动分解读说明】",
        f"   • 联动分 = 个股排名分(4分) + 板块加成(6分) + 赚钱效应暴击(5分)",
        f"   • 第一梯队：主战场龙头，攻击性最强，适合重仓突击",
        f"   • 第二梯队：板块中军，适合分散配置，等待轮动",
        f"   • 第三梯队：独立龙头，适合逆向思维，防守反击",
        f"   • 本次筛选：共{total_qualified}只股票入池，过滤了如'中国平安'等无效资金干扰",
        "=" * 80
    ]

    report_lines.extend(explanation_lines)
    for line in explanation_lines:
        print(line)

    # 将报告内容合并为字符串
    report_content = "\n".join(report_lines)

    return tier1_stocks, tier2_stocks, tier3_stocks, report_content


def _get_leader_detail_info(leader_name, df_sectors):
    """
    V9.0 辅助函数：获取龙头板块的详细评分信息

    参数:
    - leader_name: 板块名称
    - df_sectors: 板块DataFrame，包含龙头评分信息

    返回:
    - dict: 包含龙头分、涨停数、连板高度等信息，如果未找到则返回None
    """
    if df_sectors is None or df_sectors.empty:
        return None

    try:
        # 查找对应的板块行
        matching_rows = df_sectors[df_sectors['名称'] == leader_name]
        if matching_rows.empty:
            return None

        row = matching_rows.iloc[0]
        return {
            'leadership_score': row.get('leadership_score', 0.0),
            'limit_up_count': row.get('limit_up_count', 0),
            'max_consecutive': row.get('max_consecutive', 0),
            'inflow': row.get('今日主力净流入-净额', 0)
        }
    except Exception as e:
        print(f"⚠️ 获取龙头详细信息失败: {e}")
        return None


def generate_smart_report(sector_type, market_state, max_gap, group_info, thresholds, total_score, scores,
                          competition_info=None, current_time=None, data_dir=None, mover_stocks=None, stock_flow_data=None, ignition_detector=None, df_sectors=None):
    """V9.0 智能报告生成器：生成详细的分析报告（龙头评分增强版）"""

    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]

        # 【【【修改：概念已在数据源头过滤，无需重复过滤】】】
        # 如果没有有意义的概念，跳过竞争报告
        if not leading_group:
            # 继续执行后续的断层检测逻辑
            pass
        else:
            return (f"【--- {sector_type}板块{competition_type}，无明显龙头 ---】\n"
                    f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                    f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                    f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")

    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]

    if not is_gap_found:
        # 未发现断层的报告
        # 【【【修改：概念已在数据源头过滤，无需重复过滤】】】
        if group_info["members"]:
            leader_name = group_info["members"][0]
            return (f"【--- {sector_type}板块未发现显著资金断层 ---】\n"
                    f"  当前龙头: 【{leader_name}】\n"
                    f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                    f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
        else:
            # 如果所有龙头都是无意义概念，则不显示龙头信息
            return (f"【--- {sector_type}板块未发现显著资金断层 ---】\n"
                    f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                    f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")

    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "单龙头断层"
        leader_name = group_info['members'][0]

        # 【【【V9.0 新增：获取龙头的详细评分信息】】】
        leader_detail = _get_leader_detail_info(leader_name, df_sectors)
        if leader_detail:
            leader_desc = f"情绪核心: 【{leader_name}】 (龙头分:{leader_detail['leadership_score']:.2f}, 涨停:{leader_detail['limit_up_count']}家, 最高{leader_detail['max_consecutive']}连板)"
        else:
            leader_desc = f"断层龙头: 【{leader_name}】"
    else:
        gap_type = f"Top {group_info['size']} 领先集团"
        leader_name = group_info['members'][0]  # 取第一个作为代表

        # 【【【V9.0 新增：获取领先集团的详细评分信息】】】
        leader_details = []
        for member in group_info['members']:
            detail = _get_leader_detail_info(member, df_sectors)
            if detail:
                leader_details.append(f"{member}(龙头分:{detail['leadership_score']:.2f})")
            else:
                leader_details.append(member)

        if leader_details:
            leader_desc = f"领先集团: 【{', '.join(leader_details)}】"
        else:
            leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"

    # 断层分析描述
    gap_desc = (f"在第{max_gap['position'] + 1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap'] / 1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")

    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")

    report_lines = [
        f"【★★★★★ {sector_type}板块发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]

    base_report = "\n".join(report_lines)

    # 【新增】断层龙头内部个股资金流分析
    if current_time is not None and data_dir is not None:
        internal_reports = []

        # 为每个领先集团成员生成内部分析
        for i, leader_name in enumerate(group_info['members']):
            # 获取对应的板块总资金量
            sector_total_amount = group_info["amounts"][i] if i < len(group_info["amounts"]) else group_info["amounts"][0]
            # 【【【修改：传入 mover_stocks, stock_flow_data, 和 ignition_detector】】】
            internal_report = analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type,
                                                           sector_total_amount, mover_stocks, stock_flow_data, ignition_detector)
            internal_reports.append(internal_report)

        # 合并所有内部分析报告
        all_internal_reports = "\n".join(internal_reports)
        return base_report + "\n" + all_internal_reports
    else:
        return base_report


def analyze_funding_gap_v7(df_sectors, sector_type, current_time=None, data_dir=None, mover_stocks=None, stock_flow_data=None, ignition_detector=None):
    """V7智能断层检测：基于市场状态的动态多维度分析（完整版）"""

    # 1. 数据预处理
    positive_flow_df = df_sectors[df_sectors['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    inflows = positive_flow_df.head(ANALYSIS_TOP_N)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2:
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    names = positive_flow_df.head(ANALYSIS_TOP_N)['名称'].tolist()

    # 2. 竞争激烈度分析 - 优先检查
    competition_info = analyze_market_competition(inflows, names)

    # 3. 市场状态感知
    market_state = analyze_market_state(inflows)

    # 4. 全局断层点搜索
    gap_scores, max_gap = find_all_gap_points(inflows)

    # 5. 集团识别
    group_info = identify_leading_group(inflows, max_gap["position"], names)

    # 6. 动态阈值计算
    thresholds = calculate_dynamic_thresholds(market_state)

    # 7. 综合评判
    total_score, scores = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)

    # 8. 生成智能报告（包含竞争检测、内部分析和协同警报）
    return generate_smart_report(sector_type, market_state, max_gap, group_info, thresholds, total_score, scores,
                                 competition_info, current_time, data_dir, mover_stocks, stock_flow_data, ignition_detector, df_sectors)



# 保留原函数作为备份
def analyze_funding_gap_v6_backup(df_sectors, sector_type):
    positive_flow_df = df_sectors[df_sectors['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    inflows = positive_flow_df.head(ANALYSIS_TOP_N)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2: return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    top_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')

    if top_ratio < GAP_MIN_LEAD_THRESHOLD:
        leader_name = positive_flow_df.iloc[0]['名称']
        # 【【【修改：概念已在数据源头过滤，无需重复过滤】】】
        return (f"【--- {sector_type}板块未发现显著资金断层 ---】\n"
                f"  当前龙头: 【{leader_name}】\n"
                f"  领先优势: {top_ratio:.2f}倍 (未达到最小门槛: {GAP_MIN_LEAD_THRESHOLD:.2f}倍)")

    context_ratios = []
    for i in range(1, len(inflows) - 1):
        if inflows[i + 1] > 0:
            context_ratios.append(inflows[i] / inflows[i + 1])

    max_context_ratio = max(context_ratios) if context_ratios else 1.0
    is_gap_found = top_ratio > max_context_ratio * GAP_CONTEXT_MULTIPLIER
    leader_name = positive_flow_df.iloc[0]['名称']

    # 【【【修改：概念已在数据源头过滤，无需重复过滤】】】
    report_lines = []
    if is_gap_found:
        report_lines.append(f"【★★★★★ {sector_type}板块发现资金断层! ★★★★★】")
        report_lines.append(f"  断层龙头: 【{leader_name}】")
        report_lines.append(
            f"  领先优势: {top_ratio:.2f}倍 (满足最小门槛，且显著大于背景最大差距 {max_context_ratio:.2f}倍)")
    else:
        report_lines.append(f"【--- {sector_type}板块未发现显著资金断层 ---】")
        report_lines.append(f"  当前龙头: 【{leader_name}】")
        report_lines.append(
            f"  领先优势: {top_ratio:.2f}倍 (虽超门槛，但未显著领先于背景最大差距 {max_context_ratio:.2f}倍)")

    return "\n".join(report_lines)


def analyze_acceleration_v6(df_current, df_previous):
    if df_previous.empty:
        return []

    # 使用suffixes确保列名不冲突
    merged_df = pd.merge(df_current, df_previous, on='名称', suffixes=('_curr', '_prev'))

    # 直接使用合并后的列名进行计算
    merged_df['delta'] = merged_df['今日主力净流入-净额_curr'] - merged_df['今日主力净流入-净额_prev']

    # 【修复】过滤掉前值为0或接近0的情况，避免无限大倍数
    # 只有当前值和前值都大于最小阈值时才计算比率
    valid_prev_mask = merged_df['今日主力净流入-净额_prev'] > 1000000  # 前值大于100万
    merged_df['ratio'] = merged_df['今日主力净流入-净额_curr'] / merged_df['今日主力净流入-净额_prev'].where(valid_prev_mask, float('inf'))

    accelerating_sectors = merged_df[
        (merged_df['delta'] >= ACC_MIN_DELTA_THRESHOLD) &
        (merged_df['ratio'] >= ACC_MIN_RATIO_THRESHOLD) &
        (merged_df['今日主力净流入-净额_curr'] >= ACC_MIN_INFLOW_THRESHOLD) &
        (valid_prev_mask)  # 【新增】确保前值不为0
        ]
    # 在返回结果中也加入type_curr以供显示
    return accelerating_sectors.to_dict('records')


def _analyze_lurking_sectors(all_sectors_df, stock_flow_data):
    """
    【V11.0 新增】潜伏板块识别模块
    识别出资金大幅流入但板块内无涨停的"潜伏"板块
    """
    lurking_sectors_info = {}
    if all_sectors_df is None or all_sectors_df.empty or stock_flow_data is None or stock_flow_data.empty:
        print("🔍 潜伏板块分析: 数据为空，跳过分析")
        return lurking_sectors_info

    try:
        print(f"🔍 潜伏板块分析开始: 板块数据{len(all_sectors_df)}条，个股数据{len(stock_flow_data)}条")
        # 1. 定义"潜伏候选"
        # 筛选出没有涨停的板块
        no_limit_up_sectors = all_sectors_df[all_sectors_df['limit_up_count'] == 0].copy()
        # 筛选出有涨停的板块
        has_limit_up_sectors = all_sectors_df[all_sectors_df['limit_up_count'] > 0].copy()

        print(f"🔍 无涨停板块: {len(no_limit_up_sectors)}个, 有涨停板块: {len(has_limit_up_sectors)}个")

        if no_limit_up_sectors.empty or has_limit_up_sectors.empty:
            print("🔍 潜伏板块分析: 无涨停或有涨停板块为空，跳过分析")
            return lurking_sectors_info

        # 找到有涨停板块中的最高资金流入额作为基准
        benchmark_inflow = has_limit_up_sectors['今日主力净流入-净额'].max()

        # 【修复】将资金流入转换为万元单位进行比较（数据可能以元为单位存储）
        benchmark_inflow_wan = benchmark_inflow / 10000
        threshold_wan = benchmark_inflow_wan * LURKING_SECTOR_MIN_LEAD_RATIO

        print(f"🔍 基准资金流入: {benchmark_inflow_wan:.2f}万, 需要达到: {threshold_wan:.2f}万")

        # 调试：显示有涨停板块的前几名
        print("🔍 有涨停板块资金流入前5名:")
        top_limit_up = has_limit_up_sectors.nlargest(5, '今日主力净流入-净额')[['名称', '今日主力净流入-净额', 'limit_up_count']]
        for _, row in top_limit_up.iterrows():
            inflow_wan = row['今日主力净流入-净额'] / 10000
            print(f"  - {row['名称']}: {inflow_wan:.2f}万, 涨停数: {row['limit_up_count']}")

        if benchmark_inflow <= 0:
            print("🔍 潜伏板块分析: 基准资金流入<=0，跳过分析")
            return lurking_sectors_info

        # 找出资金流入是基准N倍以上的"潜伏候选"（使用万元单位比较）
        threshold = benchmark_inflow * LURKING_SECTOR_MIN_LEAD_RATIO
        candidate_mask = no_limit_up_sectors['今日主力净流入-净额'] >= threshold
        lurking_candidates = no_limit_up_sectors[candidate_mask]

        print(f"🔍 潜伏候选板块: {len(lurking_candidates)}个")
        if not lurking_candidates.empty:
            for _, row in lurking_candidates.iterrows():
                inflow_wan = row['今日主力净流入-净额'] / 10000
                print(f"  - {row['名称']}: {inflow_wan:.2f}万")

        # 【调试】显示无涨停板块资金流入前10名
        print("🔍 无涨停板块资金流入前10名:")
        top_no_limit_up = no_limit_up_sectors.nlargest(10, '今日主力净流入-净额')[['名称', '今日主力净流入-净额']]
        for _, row in top_no_limit_up.iterrows():
            inflow_wan = row['今日主力净流入-净额'] / 10000
            meets_threshold = "✓" if row['今日主力净流入-净额'] >= threshold else "✗"
            print(f"  {meets_threshold} {row['名称']}: {inflow_wan:.2f}万")

        if lurking_candidates.empty:
            print("🔍 潜伏板块分析: 无符合条件的候选板块")
            return lurking_sectors_info

        # 2. 验证"集群行为"
        top_stocks = stock_flow_data.head(50)

        for _, candidate_row in lurking_candidates.iterrows():
            sector_name = candidate_row['名称']

            # 获取该板块的所有股票
            # 注意：这里需要一种方法来获取板块内的所有股票名称
            # 暂时我们通过遍历Top50个股，反查其所属板块来实现

            count = 0
            lurking_stocks_in_sector = []
            for _, stock_row in top_stocks.iterrows():
                stock_name = stock_row['名称']
                stock_code = stock_row.get('代码', '')
                stock_sectors_info = get_stock_sectors(stock_name, stock_code)

                # 合并概念和行业进行检查
                all_stock_sectors = set(stock_sectors_info.get('concepts', []) + stock_sectors_info.get('industries', []))

                if sector_name in all_stock_sectors:
                    count += 1
                    lurking_stocks_in_sector.append(stock_name)

            # 如果出现次数达标，则确认为潜伏板块
            if count >= LURKING_SECTOR_MIN_STOCK_COUNT:
                lurking_sectors_info[sector_name] = {
                    'stocks': lurking_stocks_in_sector,
                    'count': count
                }
                print(f"🔥 **潜伏板块发现**: 【{sector_name}】 资金大幅流入且无涨停，Top50中占{count}席，高度疑似主力潜伏！")

    except Exception as e:
        print(f"⚠️ 潜伏板块分析失败: {e}")

    return lurking_sectors_info


def run_gap_analysis_backtest(date_str):
    """【V6 主函数】独立分析+加速度监控"""
    global previous_data_snapshot

    # 创建日志文件路径（总体日志文件）
    log_file_path = get_log_file_path("analysis_log", date_str)
    signal_log_path = get_log_file_path("breakthrough_signals", date_str)
    core_pool_log_path = get_log_file_path("core_pool_signals", date_str)

    # 启动时检查数据库
    print("=== 程序启动检查 ===")
    
    # 检查股票板块数据库状态
    db_file = STOCK_BLOCK_DB_PATH
    if not os.path.exists(db_file):
        db_file = STOCK_BLOCK_DB_FALLBACK
        if not os.path.exists(db_file):
            print(f"WARNING: 未找到股票板块数据库文件，已尝试路径：")
            print(f"   主路径: {STOCK_BLOCK_DB_PATH}")
            print(f"   备用路径: {STOCK_BLOCK_DB_FALLBACK}")
            print("   提示：请确保数据库文件存在，或运行red.py生成数据库")
        else:
            print(f"SUCCESS: 使用备用路径的数据库: {STOCK_BLOCK_DB_FALLBACK}")
    else:
        print(f"SUCCESS: 找到股票板块数据库: {STOCK_BLOCK_DB_PATH}")

    # 使用日志捕获上下文管理器
    with LogCapture(log_file_path, date_str) as log_capture:
        print(f"--- 开始对日期 {date_str} 进行资金断层与加速度分析 (V9.5 - 黄金法则版) ---")
        _run_analysis_core(date_str, signal_log_path, core_pool_log_path, log_capture)
        print(f"\n分析日志已保存到: {log_file_path}")
        print(f"历史突破信号日志已保存到: {signal_log_path}")
        print(f"核心股票池信号日志已保存到: {core_pool_log_path}")
        if SPLIT_LOG_BY_HOUR:
            print(f"按小时分割的日志文件已保存到: log/ 目录下")


# (新增函数，可以放在报告生成函数附近)

def generate_synergy_alert(stock_name, leader_sector_name, signal_time, reason):
    """生成并打印协同买入信号警报（V7.1 增强版）"""
    time_str = signal_time.strftime('%H:%M:%S') if hasattr(signal_time, 'strftime') else str(signal_time)

    alert_message = f"""
#########################################################
# 🚨 协同买入信号 (龙头板块 + 资金异动) 🚨
# -------------------------------------------------------
# 时间: {time_str}
# 龙头板块: 【{leader_sector_name}】
# 异动个股: 【{stock_name}】
# 触发原因: 【{reason}】
# 信号来源: 侦测到大买盘/大笔买入信号!
# 行动建议: 重点关注，可能成为板块内补涨或加强龙头。
#########################################################
"""
    print(alert_message)


def parse_movers_data(file_path):
    """解析大买盘/大笔买入文件，返回股票名称列表"""
    try:
        # 兼容不同列名，例如 '名称', 'name', '股票名称'
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')
        name_col = next((col for col in ['名称', 'name', '股票名称'] if col in df.columns), None)
        if name_col:
            return df[name_col].tolist()
        return []
    except Exception as e:
        print(f"解析异动文件失败: {e}")
        return []


def parse_big_deal_data(file_path):
    """解析大单买盘文件，返回股票信息"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码']

        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None)

        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析大单买盘文件失败: {e}")
        return []


def parse_board_changes_data(file_path):
    """解析板块异动文件，返回异动信息"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码']
        board_columns = ['板块', 'sector', '概念', 'concept', '所属板块']

        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None)
        board_col = next((col for col in board_columns if col in df.columns), None)

        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                if board_col and pd.notna(row[board_col]):
                    stock_info['板块'] = row[board_col]
                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析板块异动文件失败: {e}")
        return []


def parse_limit_up_pool_data(file_path):
    """解析涨停股池文件，返回涨停股票信息"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码']
        price_columns = ['最新价', 'price', '现价', '股价']

        # 新增列名映射
        additional_columns = {
            '成交额': ['成交额', 'volume', '成交量'],
            '封板资金': ['封板资金', 'seal_amount', '封单金额'],
            '首次封板时间': ['首次封板时间', 'first_seal_time', '首封时间'],
            '最后封板时间': ['最后封板时间', 'last_seal_time', '末封时间'],
            '炸板次数': ['炸板次数', 'break_count', '开板次数'],
            '涨停统计': ['涨停统计', 'limit_up_stat', '涨停次数'],
            '连板数': ['连板数', 'consecutive_days', '连续涨停'],
            '所属行业': ['所属行业', 'industry', '行业', '板块']
        }

        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None)
        price_col = next((col for col in price_columns if col in df.columns), None)

        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                if price_col and pd.notna(row[price_col]):
                    stock_info['最新价'] = row[price_col]

                # 添加新的字段
                for target_col, possible_names in additional_columns.items():
                    for possible_name in possible_names:
                        if possible_name in df.columns and pd.notna(row[possible_name]):
                            stock_info[target_col] = row[possible_name]
                            break

                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析涨停股池文件失败: {e}")
        return []


def parse_new_high_indicator_data(file_path):
    """解析创月新高指标文件，返回股票名称列表"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        name_col = next((col for col in name_columns if col in df.columns), None)

        if name_col:
            return df[name_col].tolist()
        return []
    except Exception as e:
        print(f"解析创月新高指标文件失败: {e}")
        return []


def _run_analysis_core(date_str, signal_log_path=None, core_pool_log_path=None, log_capture=None):
    """核心分析逻辑"""
    global previous_data_snapshot

    # 【【【核心修改：在循环外只创建一次检测器】】】
    ignition_detector = StockFlowIgnitionDetector()

    # 【【【新增：历史突破信号检测器】】】
    historical_breakthrough_detector = HistoricalBreakthroughDetector()

    # 【【【新增：盘口突袭信号追踪器 - 三步确认法】】】
    order_flow_tracker = {}  # 追踪密集盘口异动 {stock_name: [timestamps]}

    # 【【【新增：主线强度评分器】】】
    mainline_scorer = MainlineStrengthScorer()

    # 【【【新增：初始化所有日志文件 - 覆盖写入模式】】】
    if signal_log_path:
        with open(signal_log_path, 'w', encoding='utf-8') as f:
            f.write(f"=== 历史突破信号检测日志 - {date_str} ===\n")
            f.write("检测信号类型：二次点火、横空出世、突破强化、盘口突袭\n")
            f.write("=" * 60 + "\n\n")

    # 初始化核心股票池日志文件
    if core_pool_log_path:
        with open(core_pool_log_path, 'w', encoding='utf-8') as f:
            f.write(f"=== 核心股票池三梯队分析日志 - {date_str} ===\n")
            f.write("分析策略：顶级游资主线聚焦策略\n")
            f.write("梯队分类：龙头中的龙头、板块内中军、独立趋势龙头\n")
            f.write("=" * 60 + "\n\n")

    data_dir = os.path.join(BASE_DATA_DIR, date_str)
    if not os.path.isdir(data_dir):
        print(f"错误: 目录不存在 {data_dir}")
        return

    all_files = os.listdir(data_dir)
    # 修复文件匹配逻辑：支持多种文件名格式
    all_files = os.listdir(data_dir)

    # 使用新的分类函数筛选文件
    industry_files = []
    concept_files = []
    stock_flow_files = []
    big_deal_files = []
    board_changes_files = []
    limit_up_files = []
    new_high_files = []

    for f in all_files:
        if f.endswith('.csv'):
            file_type = classify_file_type(f)
            if file_type == 'sector':
                industry_files.append(f)
            elif file_type == 'concept':
                concept_files.append(f)
            elif file_type == 'stock_flow':
                stock_flow_files.append(f)
            elif file_type == 'big_deal':
                big_deal_files.append(f)
            elif file_type == 'board_changes':
                board_changes_files.append(f)
            elif file_type == 'limit_up':
                limit_up_files.append(f)
            elif file_type == 'new_high':
                new_high_files.append(f)

    industry_files = sorted(industry_files)
    concept_files = sorted(concept_files)
    stock_flow_files = sorted(stock_flow_files)
    big_deal_files = sorted(big_deal_files)
    board_changes_files = sorted(board_changes_files)
    limit_up_files = sorted(limit_up_files)
    new_high_files = sorted(new_high_files)

    print(f"文件扫描结果：找到 {len(industry_files)} 个行业文件，{len(concept_files)} 个概念文件，{len(stock_flow_files)} 个个股资金流文件，"
          f"{len(big_deal_files)} 个大单买盘文件，{len(board_changes_files)} 个板块异动文件，"
          f"{len(limit_up_files)} 个涨停股池文件，{len(new_high_files)} 个创月新高文件。")

    if not (industry_files or concept_files or stock_flow_files or big_deal_files or
            board_changes_files or limit_up_files or new_high_files):
        return

    # 修复时间戳提取逻辑：支持多种时间戳格式
    timestamps = set()
    all_data_files = (industry_files + concept_files + stock_flow_files +
                     big_deal_files + board_changes_files + limit_up_files + new_high_files)
    for f in all_data_files:
        timestamp = extract_timestamp_from_filename(f)
        if timestamp:
            timestamps.add(timestamp)
    timestamps = sorted(list(timestamps))

    for ts_str in timestamps:
        current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()

        if not (datetime.strptime("09:30", "%H:%M").time() <= current_sim_time <= datetime.strptime("11:30",
                                                                                                    "%H:%M").time() or \
                current_sim_time >= datetime.strptime("13:00", "%H:%M").time()):
            continue

        # 【新增】更新LogCapture的按小时日志文件
        if log_capture:
            log_capture.update_hourly_log(current_sim_time)

        # 【新增】获取当前时间点对应的按小时分割日志文件路径
        if SPLIT_LOG_BY_HOUR:
            hourly_analysis_log = get_log_file_path("analysis_log", date_str, current_sim_time)
            hourly_signal_log = get_log_file_path("breakthrough_signals", date_str, current_sim_time)
            hourly_core_pool_log = get_log_file_path("core_pool_signals", date_str, current_sim_time)
        else:
            hourly_analysis_log = None
            hourly_signal_log = signal_log_path
            hourly_core_pool_log = core_pool_log_path

            print("\n" + "=" * 25 + f" 模拟时间点: {current_sim_time} " + "=" * 25)

        # 初始化市场快照字典
        market_snapshot = {
            'stock_gap_leader': '',
            'sector_gap_leaders': [],
            'sector_rankings': {},
            'consecutive_board_leaders': {},
            'limit_up_leaders': {},
            'accelerating_sectors': [],
            # --- 新增 V10.2 ---
            'absolute_mainline_sectors': set(),  # 存储绝对主线板块名称
            'limit_up_counts_by_sector': {}  # 存储每个板块的涨停数
        }

        # 【【【新增逻辑：读取“大买盘”异动文件】】】
        mover_files = [f for f in all_files if 'movers_有大买盘' in f or 'movers_大笔买入' in f]
        latest_mover_file = find_latest_file(mover_files, current_sim_time)
        mover_stocks = []
        if latest_mover_file:
            mover_stocks = parse_movers_data(os.path.join(data_dir, latest_mover_file))
        # 【【【新增逻辑结束】】】

        # 读取各种新文件类型
        # 1. 大单买盘文件
        latest_big_deal_file = find_latest_file(big_deal_files, current_sim_time)
        big_deal_stocks = []
        if latest_big_deal_file:
            big_deal_stocks = parse_big_deal_data(os.path.join(data_dir, latest_big_deal_file))

        # 2. 板块异动文件
        latest_board_changes_file = find_latest_file(board_changes_files, current_sim_time)
        board_changes_stocks = []
        if latest_board_changes_file:
            board_changes_stocks = parse_board_changes_data(os.path.join(data_dir, latest_board_changes_file))

        # 3. 涨停股池文件
        latest_limit_up_file = find_latest_file(limit_up_files, current_sim_time)
        current_limit_up_stocks = []
        if latest_limit_up_file:
            current_limit_up_stocks = parse_limit_up_pool_data(os.path.join(data_dir, latest_limit_up_file))

        # 4. 创月新高指标文件
        latest_new_high_file = find_latest_file(new_high_files, current_sim_time)
        new_high_stocks = []
        if latest_new_high_file:
            new_high_stocks = parse_new_high_indicator_data(os.path.join(data_dir, latest_new_high_file))

        # 【【【盘口突袭信号捕获模块 - 第一步：捕获密集盘口异动】】】
        current_time_dt = datetime.combine(datetime.today(), current_sim_time)

        # 记录当前时间点的大买盘股票
        for stock_name in mover_stocks:
            if stock_name not in order_flow_tracker:
                order_flow_tracker[stock_name] = []
            order_flow_tracker[stock_name].append(current_time_dt)

        # 清理超出时间窗口的记录（过去5分钟）
        from datetime import timedelta
        window_minutes = 5
        cutoff_time = current_time_dt - timedelta(minutes=window_minutes)

        for stock_name in list(order_flow_tracker.keys()):
            # 过滤掉超出时间窗口的记录
            order_flow_tracker[stock_name] = [
                ts for ts in order_flow_tracker[stock_name]
                if ts >= cutoff_time
            ]
            # 如果没有记录了，删除该股票
            if not order_flow_tracker[stock_name]:
                del order_flow_tracker[stock_name]

        # 识别密集盘口异动候选股（过去5分钟内出现3次以上大买盘）
        min_signal_count = 3
        intensive_order_flow_candidates = []
        for stock_name, timestamps in order_flow_tracker.items():
            if len(timestamps) >= min_signal_count:
                intensive_order_flow_candidates.append({
                    'stock_name': stock_name,
                    'signal_count': len(timestamps),
                    'latest_time': max(timestamps)
                })

        if intensive_order_flow_candidates:
            if DEBUG_MODE:
                print(f"\n🔥 发现 {len(intensive_order_flow_candidates)} 只密集盘口异动候选股:")
                for candidate in intensive_order_flow_candidates:
                    print(f"  【{candidate['stock_name']}】- {candidate['signal_count']}次大买盘信号")
        # 【【【盘口突袭信号捕获模块结束】】】

        # 合并大单买盘数据
        all_big_deal_stocks = mover_stocks + [stock['名称'] for stock in big_deal_stocks]

        latest_industry_file = find_latest_file(industry_files, current_sim_time)
        latest_concept_file = find_latest_file(concept_files, current_sim_time)
        all_sectors_list = []
        if latest_industry_file:
            try:
                df_ind = pd.read_csv(os.path.join(data_dir, latest_industry_file), encoding='utf-8-sig',
                                     on_bad_lines='skip')
                if '名称' in df_ind.columns and '今日主力净流入-净额' in df_ind.columns:
                    df_ind['type'] = '行业'
                    all_sectors_list.append(df_ind[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"读取行业文件失败: {e}")
        if latest_concept_file:
            try:
                df_con = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig',
                                     on_bad_lines='skip')

                rename_needed = False
                rename_map = {}

                if '行业' in df_con.columns and '名称' not in df_con.columns:
                    rename_map['行业'] = '名称'
                    rename_needed = True

                if '净额' in df_con.columns and '今日主力净流入-净额' not in df_con.columns:
                    rename_map['净额'] = '今日主力净流入-净额'
                    rename_needed = True

                if rename_needed:
                    df_con.rename(columns=rename_map, inplace=True)

                if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
                    sample_value = df_con['今日主力净流入-净额'].iloc[0] if len(df_con) > 0 else 0
                    df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'].apply(convert_to_float)
                    if sample_value != 0 and abs(sample_value) < 1000:
                        df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'] * 1e8
                    df_con['type'] = '概念'
                    all_sectors_list.append(df_con[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"读取概念文件失败: {e}")

        if not all_sectors_list: continue

        all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称'])
        all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
        all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
        all_sectors_df.dropna(subset=['今日主力净流入-净额'], inplace=True)

        # 【【【性能优化：在板块数据源头高效过滤无意义概念】】】
        if not all_sectors_df.empty:
            # 使用向量化操作高效过滤概念，保留行业数据
            concept_mask = all_sectors_df['type'] == '概念'
            if concept_mask.any():
                # 获取无意义概念集合（使用统一的过滤器模块）
                meaningless_items = get_meaningless_items()
                # 使用向量化操作过滤概念
                meaningful_concept_mask = ~all_sectors_df['名称'].isin(meaningless_items)
                # 保留所有行业数据和有意义的概念数据
                all_sectors_df = all_sectors_df[~concept_mask | meaningful_concept_mask]

        # 【【【V9.0 - 第三步：集成新龙头评分体系】】】
        if not all_sectors_df.empty:
            print(f"\n🎯 正在计算板块龙头评分 (基于涨停为王哲学)...")
            # 调用龙头评分函数 V9.5 (黄金法则版)
            all_sectors_df_sorted = calculate_sector_leadership_score_v9_5(all_sectors_df, current_limit_up_stocks)

            # 【【【第二步：板块背离风险信号检测】】】
            # 在涨停股池和板块数据都准备好后，进行板块背离风险信号检测
            divergence_warnings = detect_divergence_signals(current_limit_up_stocks, all_sectors_df)
            if divergence_warnings:
                print(f"\n🚨 发现 {len(divergence_warnings)} 个板块背离风险信号:")
                for stock_name, warning_msg in divergence_warnings.items():
                    print(f"   【{stock_name}】: {warning_msg}")
            else:
                print(f"\n✅ 板块背离风险检测完成，未发现高风险信号")

            # 按龙头分从高到低排序
            if 'leadership_score' in all_sectors_df_sorted.columns:
                all_sectors_df_sorted = all_sectors_df_sorted.sort_values('leadership_score', ascending=False)

                # 分别显示概念和行业的龙头评分排行榜
                concepts_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '概念']
                industries_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '行业']

                # 概念板块龙头评分排行榜
                if not concepts_df.empty:
                    # 【修复】过滤掉净流入为0的概念（这些通常是数据补全的无效概念）
                    meaningful_concepts_df = concepts_df[concepts_df['今日主力净流入-净额'] > 0]

                    # 【新增】过滤TDX和无意义概念
                    from concept_sector_filter import is_meaningful_concept
                    if not meaningful_concepts_df.empty:
                        meaningful_concepts_df = meaningful_concepts_df[
                            meaningful_concepts_df['名称'].apply(is_meaningful_concept)
                        ]

                    if not meaningful_concepts_df.empty:
                        print(f"\n--- 📊 概念板块龙头评分排行榜 Top 10 (模拟时间点: {current_sim_time}) ---")
                        top_concepts = meaningful_concepts_df.head(10)
                    else:
                        print(f"\n--- 📊 概念板块龙头评分排行榜 (模拟时间点: {current_sim_time}) ---")
                        print("⚠️ 暂无有效的概念资金流数据")
                        top_concepts = pd.DataFrame()  # 空DataFrame

                    # 只有当有有效概念数据时才生成表格
                    if not top_concepts.empty:
                        concept_table_data = []
                        for idx, row in top_concepts.iterrows():
                            concept_table_data.append([
                                len(concept_table_data) + 1,  # 排名
                                row['名称'],  # 板块名称
                                f"{row['leadership_score']:.3f}",  # 龙头分
                                row.get('limit_up_count', 0),  # 涨停数
                                row.get('max_consecutive', 0),  # 最高连板
                                format_amount(row['今日主力净流入-净额']),  # 净流入
                            ])

                        print(tabulate(concept_table_data,
                                     headers=['排名', '概念名称', '龙头分', '涨停数', '最高连板', '净流入'],
                                     tablefmt='psql', showindex=False))

                # 行业板块龙头评分排行榜
                if not industries_df.empty:
                    # 【新增】过滤TDX和无意义行业
                    from concept_sector_filter import is_meaningful_concept
                    filtered_industries_df = industries_df[
                        industries_df['名称'].apply(is_meaningful_concept)
                    ]

                    if not filtered_industries_df.empty:
                        print(f"\n--- 📊 行业板块龙头评分排行榜 Top 10 (模拟时间点: {current_sim_time}) ---")
                        top_industries = filtered_industries_df.head(10)

                        industry_table_data = []
                        for idx, row in top_industries.iterrows():
                            industry_table_data.append([
                                len(industry_table_data) + 1,  # 排名
                                row['名称'],  # 板块名称
                                f"{row['leadership_score']:.3f}",  # 龙头分
                                row.get('limit_up_count', 0),  # 涨停数
                                row.get('max_consecutive', 0),  # 最高连板
                                format_amount(row['今日主力净流入-净额']),  # 净流入
                            ])

                        print(tabulate(industry_table_data,
                                     headers=['排名', '行业名称', '龙头分', '涨停数', '最高连板', '净流入'],
                                     tablefmt='psql', showindex=False))
                    else:
                        print(f"\n--- 📊 行业板块龙头评分排行榜 (模拟时间点: {current_sim_time}) ---")
                        print("⚠️ 暂无有效的行业资金流数据")

                # 更新all_sectors_df为排序后的版本，确保后续分析基于新排序
                all_sectors_df = all_sectors_df_sorted

                # --- V10.2 绝对主线识别逻辑 ---
                if not all_sectors_df.empty and current_limit_up_stocks:
                    # 1. 计算每个板块的涨停数 (修复：与龙头评分系统保持一致)
                    sector_limit_up_counts = {}
                    for stock in current_limit_up_stocks:
                        stock_name = stock.get('名称')
                        stock_code = stock.get('代码')

                        # 【修复】优先使用涨停股池文件中的行业信息，确保与表格显示一致
                        file_industry = stock.get('所属行业', '')

                        # 获取数据库中的概念和行业信息作为补充
                        sectors_info = get_stock_sectors(stock_name, stock_code)
                        if sectors_info is None:
                            sectors_info = {'concepts': [], 'industries': []}

                        # 构建完整的板块列表：概念 + 文件行业 + 数据库行业
                        all_stock_sectors = sectors_info.get('concepts', [])
                        if file_industry and file_industry != '未知':
                            all_stock_sectors.append(file_industry)
                        # 添加数据库中的行业信息作为补充
                        all_stock_sectors.extend(sectors_info.get('industries', []))

                        for sector in set(all_stock_sectors):
                            sector_limit_up_counts[sector] = sector_limit_up_counts.get(sector, 0) + 1
                    market_snapshot['limit_up_counts_by_sector'] = sector_limit_up_counts

                    # 2. 识别绝对主线板块 (V10.3 增强版)
                    absolute_mainline_set = set()

                    # 人气标准：涨停数达标直接认定
                    for sector, count in sector_limit_up_counts.items():
                        if count >= ABSOLUTE_MAINLINE_LIMIT_UP_THRESHOLD:
                            absolute_mainline_set.add(sector)

                    # 资金标准：分为"常规资金主线"和"巨量资金主线"
                    sorted_by_capital = all_sectors_df.sort_values(by='今日主力净流入-净额', ascending=False)

                    # --- 新增 V10.3：巨量资金主线特别通道 (资金断层) ---
                    if len(sorted_by_capital) >= 2:
                        top1_sector = sorted_by_capital.iloc[0]
                        top2_sector = sorted_by_capital.iloc[1]

                        top1_name = top1_sector['名称']
                        top1_inflow = top1_sector['今日主力净流入-净额']
                        top2_inflow = top2_sector['今日主力净流入-净额']

                        # 条件1: 绝对金额够大
                        is_absolute_strong = top1_inflow >= OVERWHELMING_CAPITAL_INFLOW_THRESHOLD
                        # 条件2: 相对优势够明显 (形成资金断层)
                        is_relative_dominant = top2_inflow > 0 and (top1_inflow / top2_inflow) >= OVERWHELMING_CAPITAL_LEAD_RATIO

                        if is_absolute_strong and is_relative_dominant:
                            # 即使没有涨停，也破格认定为绝对主线
                            absolute_mainline_set.add(top1_name)
                            print(f"🔥 巨量资金主线识别(V10.3): 板块【{top1_name}】资金流入达{format_amount(top1_inflow)}，形成资金断层(领先第二名{top1_inflow / top2_inflow:.2f}倍)，破格认定为绝对主线！")

                    # --- 原有逻辑：常规资金主线 (资金排名靠前 + 必须有涨停支撑) ---
                    top_capital_sectors = sorted_by_capital.head(ABSOLUTE_MAINLINE_CAPITAL_RANK_THRESHOLD)['名称'].tolist()

                    for sector in top_capital_sectors:
                        # 如果已经被巨量资金逻辑认定，则跳过
                        if sector in absolute_mainline_set:
                            continue

                        # 该板块必须至少有N家涨停，才算有效的资金主线
                        if sector_limit_up_counts.get(sector, 0) >= CAPITAL_MAINLINE_MIN_LIMIT_UP_SUPPORT:
                            absolute_mainline_set.add(sector)
                        else:
                            # 增加日志，方便追踪被过滤的板块
                            print(f"ℹ️ 资金主线过滤: 板块【{sector}】因无涨停股支撑(需要≥{CAPITAL_MAINLINE_MIN_LIMIT_UP_SUPPORT}个)，未被认定为常规资金主线。")

                    market_snapshot['absolute_mainline_sectors'] = absolute_mainline_set

                    if absolute_mainline_set:
                        # 【新增】过滤TDX和无意义概念后再显示
                        from concept_sector_filter import is_meaningful_concept
                        filtered_mainline_sectors = [
                            sector for sector in absolute_mainline_set
                            if is_meaningful_concept(sector)
                        ]

                        if filtered_mainline_sectors:
                            print(f"💡 盘面现实识别(V10.3)：当前绝对主线板块: {', '.join(filtered_mainline_sectors[:5])}...")
                        else:
                            print("💡 盘面现实识别(V10.3)：当前绝对主线板块: 暂无有效主线板块")

            else:
                print("⚠️ 龙头评分计算失败，使用原始排序")
        else:
            print("⚠️ 板块数据为空，跳过龙头评分计算")

        if previous_data_snapshot:
            df_previous = pd.DataFrame.from_dict(previous_data_snapshot, orient='index')
            df_previous = df_previous.reset_index().rename(columns={'index': '名称'})
        else:
            df_previous = pd.DataFrame()

        acceleration_report = analyze_acceleration_v6(all_sectors_df, df_previous)
        if acceleration_report:
            print("【▲▲▲▲▲ 资金加速度警报! ▲▲▲▲▲】")
            for item in acceleration_report:
                sector_type = item.get('type_curr', item.get('type', '未知'))
                sector_name = item['名称']
                print(f"  板块: 【{sector_name}】({sector_type})")
                print(
                    f"  资金从 {format_amount(item['今日主力净流入-净额_prev'])} 猛增至 {format_amount(item['今日主力净流入-净额_curr'])} (增速: {item['ratio']:.2f}倍)")
                
                # 填充加速板块到market_snapshot
                market_snapshot['accelerating_sectors'].append(sector_name)

        stock_flow_data = None
        stock_flow_file_format = None
        latest_stock_flow_file = find_latest_file(stock_flow_files, current_sim_time)
        if latest_stock_flow_file:
            try:
                stock_flow_file_path = os.path.join(data_dir, latest_stock_flow_file)
                stock_flow_data = parse_stock_flow_data(stock_flow_file_path, 'stock_flow')
                stock_flow_file_format = get_stock_flow_file_format(stock_flow_file_path)
                
                if stock_flow_data is not None and len(stock_flow_data) > 0:
                    # 【V11.0 新增】调用潜伏板块分析模块
                    if 'all_sectors_df_sorted' in locals() and not all_sectors_df_sorted.empty:
                        lurking_sectors_info = _analyze_lurking_sectors(all_sectors_df_sorted, stock_flow_data)
                    else:
                        lurking_sectors_info = {}

                    # 【【【修改：传入持久化的 ignition_detector】】】
                    # 显示涨停股池（在个股资金流分析之前）
                    if current_limit_up_stocks:
                        print(f"\n--- 涨停股池 Top (模拟时间点: {current_sim_time}) ---")
                        # 检查是否有上一个时间点的涨停股池数据进行对比
                        if hasattr(analyze_stock_flow_gap, 'previous_limit_up_stocks'):
                            previous_limit_up = getattr(analyze_stock_flow_gap, 'previous_limit_up_stocks', [])
                            current_names = [stock['名称'] for stock in current_limit_up_stocks]
                            previous_names = [stock['名称'] for stock in previous_limit_up] if previous_limit_up else []

                            new_limit_up = [name for name in current_names if name not in previous_names]
                            removed_limit_up = [name for name in previous_names if name not in current_names]

                            if new_limit_up:
                                print(f"🔥 新增涨停: {', '.join(new_limit_up)}")
                            if removed_limit_up:
                                print(f"❄️ 退出涨停: {', '.join(removed_limit_up)}")

                        # 统计涨停行业分布
                        industry_count = {}
                        # 【【【新增：统计涨停概念分布】】】
                        concept_count = {}

                        for stock in current_limit_up_stocks:
                            industry = stock.get('所属行业', '未知')
                            industry_count[industry] = industry_count.get(industry, 0) + 1

                            # 【【【新增：获取股票概念信息】】】
                            stock_code = stock.get('代码', '')
                            stock_name = stock.get('名称', '')
                            if stock_code or stock_name:
                                sectors_info = get_stock_sectors(stock_name, stock_code)
                                if sectors_info is None:
                                    sectors_info = {'concepts': [], 'industries': []}
                                concepts = sectors_info.get('concepts', [])
                                for concept in concepts:
                                    concept_count[concept] = concept_count.get(concept, 0) + 1

                        if industry_count:
                            max_industry = max(industry_count.items(), key=lambda x: x[1])

                            # 【【【新增：获取该行业对应的概念统计】】】
                            concept_display = ""
                            if concept_count:
                                # 只显示数量最大的概念
                                top_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)
                                if top_concepts:
                                    max_count = top_concepts[0][1]
                                    # 只显示数量等于最大值的概念
                                    max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                                    concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""

                            print(f"涨停行业最多的：{max_industry[0]}  {max_industry[1]}个{concept_display}")

                            # 填充涨停梯队强度到market_snapshot
                            market_snapshot['limit_up_leaders'][max_industry[0]] = {
                                'type': '涨停行业最多',
                                'count': max_industry[1]
                            }

                        # 统计连板数分布
                        consecutive_stats = {}
                        # 【【【新增：统计连板数对应的概念分布】】】
                        consecutive_concept_stats = {}

                        for stock in current_limit_up_stocks:
                            consecutive_days = stock.get('连板数', 1)
                            if pd.notna(consecutive_days):
                                consecutive_days = int(consecutive_days)
                            else:
                                consecutive_days = 1

                            industry = stock.get('所属行业', '未知')

                            if consecutive_days not in consecutive_stats:
                                consecutive_stats[consecutive_days] = {}
                                consecutive_concept_stats[consecutive_days] = {}

                            if industry not in consecutive_stats[consecutive_days]:
                                consecutive_stats[consecutive_days][industry] = 0

                            consecutive_stats[consecutive_days][industry] += 1

                            # 【【【新增：统计该连板数的概念分布】】】
                            stock_code = stock.get('代码', '')
                            stock_name = stock.get('名称', '')
                            if stock_code or stock_name:
                                sectors_info = get_stock_sectors(stock_name, stock_code)
                                if sectors_info is None:
                                    sectors_info = {'concepts': [], 'industries': []}
                                concepts = sectors_info.get('concepts', [])
                                for concept in concepts:
                                    if concept not in consecutive_concept_stats[consecutive_days]:
                                        consecutive_concept_stats[consecutive_days][concept] = 0
                                    consecutive_concept_stats[consecutive_days][concept] += 1

                        if consecutive_stats:
                            # 1. 连板数最多的行业
                            max_consecutive = max(consecutive_stats.keys())
                            if max_consecutive > 1:
                                max_consecutive_industries = consecutive_stats[max_consecutive]
                                max_consecutive_industry = max(max_consecutive_industries.items(), key=lambda x: x[1])

                                # 【【【新增：获取该连板数对应的概念统计】】】
                                concept_display = ""
                                if max_consecutive in consecutive_concept_stats:
                                    # 只显示数量最大的概念
                                    top_concepts = sorted(consecutive_concept_stats[max_consecutive].items(), key=lambda x: x[1], reverse=True)
                                    if top_concepts:
                                        max_count = top_concepts[0][1]
                                        # 只显示数量等于最大值的概念
                                        max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                                        concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""

                                print(f"连板数最多行业：{max_consecutive_industry[0]}  {max_consecutive_industry[1]}个  {max_consecutive}连扳{concept_display}")

                                # 填充连板梯队强度到market_snapshot
                                market_snapshot['consecutive_board_leaders'][max_consecutive_industry[0]] = {
                                    'type': '连板数最多',
                                    'consecutive_days': max_consecutive,
                                    'count': max_consecutive_industry[1]
                                }

                            # 2. 首板最多的行业
                            if 1 in consecutive_stats:
                                first_board_industries = consecutive_stats[1]
                                max_first_board_industry = max(first_board_industries.items(), key=lambda x: x[1])

                                # 【【【新增：获取首板对应的概念统计】】】
                                concept_display = ""
                                if 1 in consecutive_concept_stats:
                                    # 只显示数量最大的概念
                                    top_concepts = sorted(consecutive_concept_stats[1].items(), key=lambda x: x[1], reverse=True)
                                    if top_concepts:
                                        max_count = top_concepts[0][1]
                                        # 只显示数量等于最大值的概念
                                        max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                                        concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""

                                print(f"首扳最多行业：{max_first_board_industry[0]}  {max_first_board_industry[1]}个  首扳{concept_display}")

                                # 填充首板梯队强度到market_snapshot
                                market_snapshot['limit_up_leaders'][max_first_board_industry[0]] = {
                                    'type': '首板最多',
                                    'count': max_first_board_industry[1]
                                }

                            # 3. 2板、3板、4板等最多的行业（除了最高连板数）
                            for consecutive_days in sorted(consecutive_stats.keys()):
                                if consecutive_days > 1 and consecutive_days < max_consecutive:
                                    industries = consecutive_stats[consecutive_days]
                                    max_industry = max(industries.items(), key=lambda x: x[1])

                                    # 【【【新增：获取该连板数对应的概念统计】】】
                                    concept_display = ""
                                    if consecutive_days in consecutive_concept_stats:
                                        # 只显示数量最大的概念
                                        top_concepts = sorted(consecutive_concept_stats[consecutive_days].items(), key=lambda x: x[1], reverse=True)
                                        if top_concepts:
                                            max_count = top_concepts[0][1]
                                            # 只显示数量等于最大值的概念
                                            max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                                            concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""

                                    print(f"{consecutive_days}板连板数最多行业：{max_industry[0]}  {max_industry[1]}个  {consecutive_days}连扳{concept_display}")

                        # 显示当前涨停股池
                        display_limit_up = current_limit_up_stocks[:20]  # 显示前20只
                        if display_limit_up:
                            # 为涨停股池添加历史最大值检查
                            limit_up_df = pd.DataFrame(display_limit_up)

                            # 如果涨停股池数据中没有资金流入信息，尝试从个股资金流数据中匹配
                            if stock_flow_data is not None and not stock_flow_data.empty:
                                # 通过股票名称匹配资金流入数据
                                for i, stock in enumerate(display_limit_up):
                                    stock_name = stock.get('名称', '')
                                    # 修复pandas Series布尔值歧义问题
                                    name_mask = stock_flow_data['名称'] == stock_name
                                    matching_flow = stock_flow_data.loc[name_mask]
                                    if not matching_flow.empty:
                                        flow_row = matching_flow.iloc[0]
                                        # 添加资金流入信息到涨停股池数据
                                        display_limit_up[i]['今日主力净流入-净额'] = flow_row.get('今日主力净流入-净额', 0)
                                        display_limit_up[i]['今日主力净流入-净占比'] = flow_row.get('今日主力净流入-净占比', 0)
                                        if '代码' not in display_limit_up[i] and '代码' in flow_row:
                                            display_limit_up[i]['代码'] = flow_row['代码']

                            # 重新创建DataFrame并检查历史最大值
                            limit_up_df = pd.DataFrame(display_limit_up)
                            if '今日主力净流入-净额' in limit_up_df.columns:
                                limit_up_df = check_historical_max_inflow(limit_up_df, BACKTEST_DATE)
                            else:
                                limit_up_df['大于历史资金流入'] = ''

                            limit_up_data = []
                            for i, (_, stock) in enumerate(limit_up_df.iterrows(), 1):
                                row = [i, stock['名称']]

                                # 基础信息
                                if '代码' in stock and pd.notna(stock['代码']):
                                    row.append(stock['代码'])
                                if '最新价' in stock:
                                    row.append(f"{stock['最新价']:.2f}" if pd.notna(stock['最新价']) else 'N/A')

                                # 新增字段
                                if '成交额' in stock:
                                    amount = stock['成交额']
                                    if pd.notna(amount) and amount > 0:
                                        row.append(f"{amount/1e8:.2f}亿" if amount >= 1e8 else f"{amount/1e4:.0f}万")
                                    else:
                                        row.append('N/A')

                                if '封板资金' in stock:
                                    seal_amount = stock['封板资金']
                                    if pd.notna(seal_amount) and seal_amount > 0:
                                        row.append(f"{seal_amount/1e8:.2f}亿" if seal_amount >= 1e8 else f"{seal_amount/1e4:.0f}万")
                                    else:
                                        row.append('N/A')

                                if '首次封板时间' in stock:
                                    row.append(str(stock['首次封板时间']) if pd.notna(stock['首次封板时间']) else 'N/A')

                                if '最后封板时间' in stock:
                                    row.append(str(stock['最后封板时间']) if pd.notna(stock['最后封板时间']) else 'N/A')

                                if '炸板次数' in stock:
                                    row.append(str(int(stock['炸板次数'])) if pd.notna(stock['炸板次数']) else '0')

                                if '涨停统计' in stock:
                                    row.append(str(stock['涨停统计']) if pd.notna(stock['涨停统计']) else 'N/A')

                                if '连板数' in stock:
                                    row.append(str(int(stock['连板数'])) if pd.notna(stock['连板数']) else '1')

                                if '所属行业' in stock:
                                    row.append(str(stock['所属行业']) if pd.notna(stock['所属行业']) else 'N/A')

                                # 添加大于历史资金流入标记
                                hist_max_flag = stock.get('大于历史资金流入', '')
                                row.append(hist_max_flag)

                                # 【【【第三步：添加板块背离风险警示】】】
                                # 检查该股票是否存在板块背离风险
                                risk_warning = ""
                                if 'divergence_warnings' in locals() and divergence_warnings:
                                    stock_name = stock['名称']
                                    if stock_name in divergence_warnings:
                                        risk_warning = "【高位风险警示】"
                                row.append(risk_warning)

                                limit_up_data.append(row)

                            # 构建表头
                            headers = ['排名', '股票名称']
                            if any('代码' in stock for stock in display_limit_up):
                                headers.append('股票代码')
                            if any('最新价' in stock for stock in display_limit_up):
                                headers.append('最新价')
                            if any('成交额' in stock for stock in display_limit_up):
                                headers.append('成交额')
                            if any('封板资金' in stock for stock in display_limit_up):
                                headers.append('封板资金')
                            if any('首次封板时间' in stock for stock in display_limit_up):
                                headers.append('首次封板时间')
                            if any('最后封板时间' in stock for stock in display_limit_up):
                                headers.append('最后封板时间')
                            if any('炸板次数' in stock for stock in display_limit_up):
                                headers.append('炸板次数')
                            if any('涨停统计' in stock for stock in display_limit_up):
                                headers.append('涨停统计')
                            if any('连板数' in stock for stock in display_limit_up):
                                headers.append('连板数')
                            if any('所属行业' in stock for stock in display_limit_up):
                                headers.append('所属行业')
                            # 添加大于历史资金流入列
                            headers.append('大于历史资金流入')
                            # 【【【第三步：添加风险警示列到表头】】】
                            headers.append('风险警示')

                            print(tabulate(limit_up_data, headers=headers, tablefmt='psql'))

                        # 保存当前涨停股池数据供下次对比
                        analyze_stock_flow_gap.previous_limit_up_stocks = current_limit_up_stocks
                    else:
                        print(f"\n--- 涨停股池 Top (模拟时间点: {current_sim_time}) ---")
                        print("暂无涨停股池数据。")

                    stock_flow_report = analyze_stock_flow_gap(stock_flow_data, current_sim_time, data_dir,
                                                               ignition_detector, stock_flow_file_format)
                    print(stock_flow_report)
                    
                    # 从个股资金流报告中提取断层龙头信息
                    if "个股资金流发现资金断层" in stock_flow_report and "断层龙头:" in stock_flow_report:
                        # 使用正则表达式提取断层龙头名称
                        import re
                        gap_leader_match = re.search(r'断层龙头: 【([^】]+)】', stock_flow_report)
                        if gap_leader_match:
                            market_snapshot['stock_gap_leader'] = gap_leader_match.group(1)

                    print(f"\n--- 个股资金流入 Top 50 (模拟时间点: {current_sim_time}) ---")
                    # 使用格式感知的过滤策略，确保与断层检测一致
                    positive_stocks = apply_stock_filter(stock_flow_data, stock_flow_file_format)
                    if not positive_stocks.empty:
                        # 【新增】过滤ST股票
                        if '名称' in positive_stocks.columns:
                            # 过滤掉股票名称中包含ST、*ST、ST*等的股票
                            st_mask = positive_stocks['名称'].str.contains(r'\*?ST\*?', case=False, na=False)
                            positive_stocks = positive_stocks[~st_mask]

                        display_stocks = positive_stocks.head(50).copy()

                        # 添加历史最大值检查
                        display_stocks = check_historical_max_inflow(display_stocks, BACKTEST_DATE)

                        # 【【【历史突破信号检测将在板块分析完成后进行】】】

                        display_columns = ['排名', '名称']
                        headers = ['排名', '股票名称']
                        if '今日涨跌幅' in display_stocks.columns:
                            # 检查是否所有涨跌幅都是0（开盘初期常见情况）
                            non_zero_changes = display_stocks['今日涨跌幅'][display_stocks['今日涨跌幅'] != 0]
                            if len(non_zero_changes) > 0:  # 有非零涨跌幅数据才显示
                                display_stocks['涨跌幅'] = display_stocks['今日涨跌幅'].apply(
                                    lambda x: f"{x:.2f}%" if pd.notna(x) else 'N/A')
                                display_columns.append('涨跌幅')
                                headers.append('今日涨跌幅')
                            # 如果所有涨跌幅都是0，则不显示涨跌幅列（避免显示一列0.00%）
                        display_stocks['主力净流入-净额'] = display_stocks['今日主力净流入-净额'].apply(format_amount)
                        display_columns.append('主力净流入-净额')
                        headers.append('今日主力净流入-净额')
                        if '今日主力净流入-净占比' in display_stocks.columns:
                            display_stocks['主力净流入-净占比'] = display_stocks['今日主力净流入-净占比'].apply(
                                lambda x: f"{x:.2f}%" if pd.notna(x) else 'N/A')
                            display_columns.append('主力净流入-净占比')
                            headers.append('今日主力净流入-净占比')

                        # 添加创月新高标记
                        if new_high_stocks:
                            display_stocks['创月新高'] = display_stocks['名称'].apply(
                                lambda x: '🔥' if x in new_high_stocks else '')
                            display_columns.append('创月新高')
                            headers.append('创月新高')

                        # 保持原始排名，不重新分配
                        display_stocks['排名'] = display_stocks.index + 1

                        # 【【【新增：添加概念涨停数量和行业涨停数量列 - 性能优化版】】】
                        # 预先构建涨停股票的概念和行业映射，避免重复查询
                        limit_up_concept_map = {}  # concept -> count
                        limit_up_industry_map = {}  # industry -> count

                        for limit_up_stock in current_limit_up_stocks:
                            limit_up_name = limit_up_stock.get('名称', '')
                            limit_up_code = limit_up_stock.get('代码', '')
                            limit_up_industry = limit_up_stock.get('所属行业', '')

                            # 统计行业涨停数量
                            if limit_up_industry:
                                limit_up_industry_map[limit_up_industry] = limit_up_industry_map.get(limit_up_industry, 0) + 1

                            # 获取概念信息并统计
                            if limit_up_name or limit_up_code:
                                limit_up_sectors = get_stock_sectors(limit_up_name, limit_up_code)
                                for concept in limit_up_sectors.get('concepts', []):
                                    limit_up_concept_map[concept] = limit_up_concept_map.get(concept, 0) + 1

                        concept_limit_up_counts = []
                        industry_limit_up_counts = []

                        for _, row in display_stocks.iterrows():
                            stock_name = row['名称']
                            stock_code = row.get('代码', '')

                            # 获取股票的概念和行业信息
                            sectors_info = get_stock_sectors(stock_name, stock_code)
                            if sectors_info is None:
                                sectors_info = {'concepts': [], 'industries': []}
                            stock_concepts = sectors_info.get('concepts', [])
                            stock_industries = sectors_info.get('industries', [])

                            # 统计该股票所属概念的涨停数量（使用预构建的映射）
                            concept_count = max([limit_up_concept_map.get(concept, 0) for concept in stock_concepts] + [0])

                            # 统计该股票所属行业的涨停数量（使用预构建的映射）
                            industry_count = max([limit_up_industry_map.get(industry, 0) for industry in stock_industries] + [0])

                            concept_limit_up_counts.append(concept_count)
                            industry_limit_up_counts.append(industry_count)

                        display_stocks['概念涨停数'] = concept_limit_up_counts
                        display_stocks['行业涨停数'] = industry_limit_up_counts
                        display_columns.extend(['概念涨停数', '行业涨停数'])
                        headers.extend(['概念涨停数', '行业涨停数'])

                        # 【【【添加龙头概念和龙头板块列】】】
                        # 获取龙头板块排行榜前3名
                        top_concept_leaders = {}
                        top_industry_leaders = {}

                        # 概念板块前3名 - 使用与显示排行榜相同的过滤逻辑
                        concept_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '概念']
                        # 应用与显示排行榜相同的过滤条件
                        meaningful_concepts_df = concept_df[concept_df['今日主力净流入-净额'] > 0]
                        from concept_sector_filter import is_meaningful_concept
                        if not meaningful_concepts_df.empty:
                            meaningful_concepts_df = meaningful_concepts_df[
                                meaningful_concepts_df['名称'].apply(is_meaningful_concept)
                            ]
                        concept_df_sorted = meaningful_concepts_df.head(3)  # 取过滤后的前3名

                        for idx, (_, row) in enumerate(concept_df_sorted.iterrows(), 1):
                            top_concept_leaders[row['名称']] = idx

                        # 行业板块前3名 - 使用已排序的数据
                        industry_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '行业']
                        industry_df_sorted = industry_df.head(3)  # 已经按leadership_score排序，直接取前3名

                        for idx, (_, row) in enumerate(industry_df_sorted.head(3).iterrows(), 1):
                            top_industry_leaders[row['名称']] = idx

                        # 为每个股票添加龙头概念和龙头板块信息
                        leader_concepts = []
                        leader_industries = []

                        for _, row in display_stocks.iterrows():
                            # 兼容不同的列名格式
                            stock_name = ''
                            for name_col in ['名称', 'name', '股票名称']:
                                if name_col in row and pd.notna(row[name_col]):
                                    stock_name = str(row[name_col]).strip()
                                    break

                            stock_code = ''
                            for code_col in ['代码', 'code', '股票代码']:
                                if code_col in row and pd.notna(row[code_col]):
                                    stock_code = str(row[code_col]).strip()
                                    break

                            # 获取股票的概念和行业信息
                            sectors_info = get_stock_sectors(stock_name, stock_code)
                            # 添加安全检查，防止 sectors_info 为 None
                            if sectors_info is None:
                                sectors_info = {'concepts': [], 'industries': []}
                            stock_concepts = sectors_info.get('concepts', [])
                            stock_industries = sectors_info.get('industries', [])  # 从数据库获取行业信息

                            # 查找龙头概念（前3名）- 只显示概念，去重
                            leader_concept_info = []
                            seen_concepts = set()
                            for concept in stock_concepts:
                                if concept in top_concept_leaders and concept not in seen_concepts:
                                    rank = top_concept_leaders[concept]
                                    leader_concept_info.append(f"{rank} {concept}")
                                    seen_concepts.add(concept)

                            # 查找龙头板块（前3名）- 只显示行业，去重
                            leader_industry_info = []
                            seen_industries = set()
                            for industry in stock_industries:
                                if industry in top_industry_leaders and industry not in seen_industries:
                                    rank = top_industry_leaders[industry]
                                    leader_industry_info.append(f"{rank} {industry}")
                                    seen_industries.add(industry)

                            # 格式化显示
                            leader_concepts.append(" | ".join(leader_concept_info) if leader_concept_info else "")
                            leader_industries.append(" | ".join(leader_industry_info) if leader_industry_info else "")

                        # 添加龙头概念和龙头板块列
                        display_stocks['龙头概念'] = leader_concepts
                        display_stocks['龙头板块'] = leader_industries
                        display_columns.extend(['龙头概念', '龙头板块'])
                        headers.extend(['龙头概念', '龙头板块'])

                        # 添加大于历史资金流入列（在排名设置之后）
                        display_columns.append('大于历史资金流入')
                        headers.append('大于历史资金流入')

                        # 【V11.0 新增】为 display_stocks 添加潜伏板块标记列（放在最后以避免影响原有逻辑）
                        lurking_markers = []
                        for _, row in display_stocks.iterrows():
                            stock_name = row['名称']
                            marker = ''
                            for sector, info in lurking_sectors_info.items():
                                if stock_name in info['stocks']:
                                    marker = f"🔥潜伏中({sector})"
                                    break
                            lurking_markers.append(marker)

                        display_stocks['潜伏板块'] = lurking_markers
                        display_columns.append('潜伏板块')
                        headers.append('潜伏板块')

                        # 【新增】统计Top 50中最多的概念、行业、龙头概念和龙头板块
                        top50_concept_count = {}
                        top50_industry_count = {}
                        top50_leader_concept_count = {}
                        top50_leader_industry_count = {}

                        for _, row in display_stocks.iterrows():
                            stock_name = row['名称']
                            stock_code = row.get('代码', '')

                            # 获取股票的概念和行业信息
                            sectors_info = get_stock_sectors(stock_name, stock_code)
                            if sectors_info is None:
                                sectors_info = {'concepts': [], 'industries': []}

                            # 【修复】统计概念时过滤TDX和无意义概念
                            from concept_sector_filter import is_meaningful_concept
                            for concept in sectors_info.get('concepts', []):
                                if is_meaningful_concept(concept):  # 应用TDX和无意义概念过滤
                                    top50_concept_count[concept] = top50_concept_count.get(concept, 0) + 1

                            # 【修复】统计行业时过滤TDX和无意义行业
                            for industry in sectors_info.get('industries', []):
                                if is_meaningful_concept(industry):  # 应用TDX和无意义行业过滤
                                    top50_industry_count[industry] = top50_industry_count.get(industry, 0) + 1

                            # 【新增】统计龙头概念
                            leader_concept_text = row.get('龙头概念', '')
                            if leader_concept_text:
                                # 解析龙头概念文本，格式如 "1 人工智能 | 2 芯片概念"
                                concept_parts = leader_concept_text.split(' | ')
                                for part in concept_parts:
                                    if part.strip():
                                        # 提取概念名称（去掉排名数字）
                                        concept_name = ' '.join(part.strip().split()[1:])  # 跳过第一个数字
                                        if concept_name and is_meaningful_concept(concept_name):
                                            top50_leader_concept_count[concept_name] = top50_leader_concept_count.get(concept_name, 0) + 1

                            # 【新增】统计龙头板块
                            leader_industry_text = row.get('龙头板块', '')
                            if leader_industry_text:
                                # 解析龙头板块文本，格式如 "1 软件服务 | 2 计算机设备"
                                industry_parts = leader_industry_text.split(' | ')
                                for part in industry_parts:
                                    if part.strip():
                                        # 提取板块名称（去掉排名数字）
                                        industry_name = ' '.join(part.strip().split()[1:])  # 跳过第一个数字
                                        if industry_name and is_meaningful_concept(industry_name):
                                            top50_leader_industry_count[industry_name] = top50_leader_industry_count.get(industry_name, 0) + 1

                        # 找出最多的概念、行业、龙头概念和龙头板块
                        if top50_concept_count:
                            most_common_concept = max(top50_concept_count, key=top50_concept_count.get)
                            concept_count = top50_concept_count[most_common_concept]
                            print(f"top50 概念最多的是  {most_common_concept} ({concept_count}只)")

                        if top50_industry_count:
                            most_common_industry = max(top50_industry_count, key=top50_industry_count.get)
                            industry_count = top50_industry_count[most_common_industry]
                            print(f"top50 行业最多的是  {most_common_industry} ({industry_count}只)")

                        if top50_leader_concept_count:
                            most_common_leader_concept = max(top50_leader_concept_count, key=top50_leader_concept_count.get)
                            leader_concept_count = top50_leader_concept_count[most_common_leader_concept]
                            print(f"top50 龙头概念最多的是  {most_common_leader_concept} ({leader_concept_count}只)")

                        if top50_leader_industry_count:
                            most_common_leader_industry = max(top50_leader_industry_count, key=top50_leader_industry_count.get)
                            leader_industry_count = top50_leader_industry_count[most_common_leader_industry]
                            print(f"top50 龙头板块最多的是  {most_common_leader_industry} ({leader_industry_count}只)")

                        print(tabulate(display_stocks[display_columns], headers=headers, tablefmt='psql',
                                       showindex=False))

                        # 【【【新增：显示排名变化最大的股票】】】
                        try:
                            # 获取当前时间的排名数据（前50名）
                            current_rankings = {}
                            for idx, row in display_stocks.iterrows():
                                stock_name = row['名称']
                                current_rank = idx + 1  # 当前排名（1-50）
                                current_rankings[stock_name] = current_rank

                            # 显示各时间段排名变化最大的股票
                            display_ranking_changes(current_rankings, current_sim_time, data_dir)

                        except Exception as e:
                            print(f"排名变化分析失败: {e}")
                    else:
                        print("无正流入个股数据。")
                else:
                    print("个股资金流数据解析失败或为空。")
            except Exception as e:
                print(f"个股资金流分析失败: {e}")
        else:
            print("未找到个股资金流文件。")

        # 【【【V9.0 修改：基于龙头分排序，而非资金流入排序】】】
        df_concepts = all_sectors_df[all_sectors_df['type'] == '概念']
        if 'leadership_score' in df_concepts.columns:
            df_concepts = df_concepts.sort_values(by='leadership_score', ascending=False)
        else:
            df_concepts = df_concepts.sort_values(by='今日主力净流入-净额', ascending=False)
        # 【【【修改：传入 ignition_detector】】】
        concept_report = analyze_funding_gap_v7(df_concepts, "概念", current_sim_time, data_dir, all_big_deal_stocks,
                                                stock_flow_data, ignition_detector)
        print(concept_report)
        
        # 从概念报告中提取板块断层龙头信息
        if "概念板块发现资金断层" in concept_report:
            import re
            # 提取单个断层龙头
            single_leader_match = re.search(r'断层龙头: 【([^】]+)】', concept_report)
            if single_leader_match:
                market_snapshot['sector_gap_leaders'].append(single_leader_match.group(1))
            # 提取领先集团
            group_leader_match = re.search(r'领先集团: 【([^】]+)】', concept_report)
            if group_leader_match:
                leaders = [name.strip() for name in group_leader_match.group(1).split('、')]
                market_snapshot['sector_gap_leaders'].extend(leaders)
        
        # 提取概念板块资金排名信息
        if not df_concepts.empty:
            for i, (_, row) in enumerate(df_concepts.head(3).iterrows(), 1):
                sector_name = row['名称']
                market_snapshot['sector_rankings'][sector_name] = i

        # 显示板块异动信息
        if board_changes_stocks:
            print("\n【★★★★★ 板块异动发现! ★★★★★】")
            for stock in board_changes_stocks:
                stock_name = stock['名称']
                stock_code = stock.get('代码', 'N/A')
                board_name = stock.get('板块', 'N/A')
                print(f"  异动股票: 【{stock_name}】({stock_code}) - 所属板块: {board_name}")

                # 检查是否在概念板块断层龙头中
                if concept_report and "断层龙头:" in concept_report:
                    # 简单的文本匹配检查，实际应用中可能需要更复杂的逻辑
                    if board_name in concept_report or stock_name in concept_report:
                        print(f"    🔥 该股票所属板块【{board_name}】在概念板块资金断层中发现!")
        else:
            print("\n【板块异动】暂无板块异动数据。")

        print(f"\n--- 概念板块资金流入 Top 5 (模拟时间点: {current_sim_time}) ---")
        if not df_concepts.empty:
            # 【【【修改：概念已在数据源头过滤，无需重复过滤】】】
            if not df_concepts.empty:
                filtered_df_concepts = df_concepts
                display_concepts = filtered_df_concepts.head(5).copy()
                display_concepts['主力净流入'] = display_concepts['今日主力净流入-净额'].apply(format_amount)
                display_concepts['排名'] = range(1, len(display_concepts) + 1)

                # 【【【新增：添加涨停数量列】】】
                limit_up_counts = []
                for _, row in display_concepts.iterrows():
                    concept_name = row['名称']
                    # 统计该概念的涨停股票数量
                    concept_limit_up_count = 0
                    for stock in current_limit_up_stocks:
                        stock_code = stock.get('代码', '')
                        stock_name = stock.get('名称', '')
                        if stock_code or stock_name:
                            sectors_info = get_stock_sectors(stock_name, stock_code)
                            if sectors_info is None:
                                sectors_info = {'concepts': [], 'industries': []}
                            concepts = sectors_info.get('concepts', [])
                            if concept_name in concepts:
                                concept_limit_up_count += 1
                    limit_up_counts.append(concept_limit_up_count)

                display_concepts['涨停数量'] = limit_up_counts

                # 【V11.0 修改】为显示添加潜伏标记（不修改原始数据）
                display_concepts_copy = display_concepts.copy()
                display_concepts_copy['名称'] = display_concepts_copy['名称'].apply(
                    lambda name: f"{name} (🔥潜伏板块)" if name in lurking_sectors_info else name
                )

                print(tabulate(display_concepts_copy[['排名', '名称', '主力净流入', '涨停数量']],
                               headers=['排名', '概念名称', '主力净流入', '涨停数量'],
                               tablefmt='psql', showindex=False))

                # 【【【新增：显示涨停数量最多的概念前3名】】】
                if current_limit_up_stocks:
                    # 统计所有概念的涨停数量
                    concept_limit_up_map = {}
                    for stock in current_limit_up_stocks:
                        stock_code = stock.get('代码', '')
                        stock_name = stock.get('名称', '')
                        if stock_code or stock_name:
                            sectors_info = get_stock_sectors(stock_name, stock_code)
                            if sectors_info is None:
                                sectors_info = {'concepts': [], 'industries': []}
                            concepts = sectors_info.get('concepts', [])
                            for concept in concepts:
                                concept_limit_up_map[concept] = concept_limit_up_map.get(concept, 0) + 1

                    # 按涨停数量排序，取前3名
                    if concept_limit_up_map:
                        top_concepts = sorted(concept_limit_up_map.items(), key=lambda x: x[1], reverse=True)[:3]
                        concept_display = " | ".join([f"{concept} {count}个" for concept, count in top_concepts])
                        print(f"涨停数量最多概念: {concept_display}")
            else:
                print("无有意义的概念板块数据。")
        else:
            print("无概念板块数据。")

        print("")
        # 【【【V9.0 修改：基于龙头分排序，而非资金流入排序】】】
        df_industries = all_sectors_df[all_sectors_df['type'] == '行业']
        if 'leadership_score' in df_industries.columns:
            df_industries = df_industries.sort_values(by='leadership_score', ascending=False)
        else:
            df_industries = df_industries.sort_values(by='今日主力净流入-净额', ascending=False)

        print(f"\n--- 行业板块资金流入 Top 5 (模拟时间点: {current_sim_time}) ---")
        if not df_industries.empty:
            display_industries = df_industries.head(5).copy()
            display_industries['主力净流入'] = display_industries['今日主力净流入-净额'].apply(format_amount)
            display_industries['排名'] = range(1, len(display_industries) + 1)

            # 【【【新增：添加涨停数量列】】】
            limit_up_counts = []
            for _, row in display_industries.iterrows():
                industry_name = row['名称']
                # 统计该行业的涨停股票数量
                industry_limit_up_count = 0
                for stock in current_limit_up_stocks:
                    stock_industry = stock.get('所属行业', '')
                    if stock_industry == industry_name:
                        industry_limit_up_count += 1
                limit_up_counts.append(industry_limit_up_count)

            display_industries['涨停数量'] = limit_up_counts

            # 【V11.0 修改】为显示添加潜伏标记（不修改原始数据）
            display_industries_copy = display_industries.copy()
            display_industries_copy['名称'] = display_industries_copy['名称'].apply(
                lambda name: f"{name} (🔥潜伏板块)" if name in lurking_sectors_info else name
            )

            print(
                tabulate(display_industries_copy[['排名', '名称', '主力净流入', '涨停数量']],
                         headers=['排名', '行业名称', '主力净流入', '涨停数量'],
                         tablefmt='psql', showindex=False))

            # 【【【新增：显示涨停数量最多的行业前3名】】】
            if current_limit_up_stocks:
                # 统计所有行业的涨停数量
                industry_limit_up_map = {}
                for stock in current_limit_up_stocks:
                    stock_industry = stock.get('所属行业', '')
                    if stock_industry:
                        industry_limit_up_map[stock_industry] = industry_limit_up_map.get(stock_industry, 0) + 1

                # 按涨停数量排序，取前3名
                if industry_limit_up_map:
                    top_industries = sorted(industry_limit_up_map.items(), key=lambda x: x[1], reverse=True)[:3]
                    industry_display = " | ".join([f"{industry} {count}个" for industry, count in top_industries])
                    print(f"涨停数量最多行业: {industry_display}")
        else:
            print("无行业板块数据。")

        # 【【【修改：传入 ignition_detector】】】
        industry_report = analyze_funding_gap_v7(df_industries, "行业", current_sim_time, data_dir, all_big_deal_stocks,
                                                 stock_flow_data, ignition_detector)
        print(industry_report)
        
        # 从行业报告中提取板块断层龙头信息
        if "行业板块发现资金断层" in industry_report:
            import re
            # 提取单个断层龙头
            single_leader_match = re.search(r'断层龙头: 【([^】]+)】', industry_report)
            if single_leader_match:
                market_snapshot['sector_gap_leaders'].append(single_leader_match.group(1))
            # 提取领先集团
            group_leader_match = re.search(r'领先集团: 【([^】]+)】', industry_report)
            if group_leader_match:
                leaders = [name.strip() for name in group_leader_match.group(1).split('、')]
                market_snapshot['sector_gap_leaders'].extend(leaders)
        
        # 提取行业板块资金排名信息
        if not df_industries.empty:
            for i, (_, row) in enumerate(df_industries.head(3).iterrows(), 1):
                sector_name = row['名称']
                market_snapshot['sector_rankings'][sector_name] = i

        # 【【【现在进行历史突破信号检测，此时market_snapshot已经正确填充】】】
        # 重新获取个股数据进行信号检测
        if stock_flow_file_path and os.path.exists(stock_flow_file_path):
            try:
                stock_flow_data = parse_stock_flow_data(stock_flow_file_path, 'stock_flow')
                stock_flow_file_format = get_stock_flow_file_format(stock_flow_file_path)
                if stock_flow_data is not None and not stock_flow_data.empty:
                    positive_stocks = apply_stock_filter(stock_flow_data, stock_flow_file_format)
                    if not positive_stocks.empty:
                        display_stocks = positive_stocks.head(50).copy()
                        display_stocks = check_historical_max_inflow(display_stocks, BACKTEST_DATE)

                        # 进行历史突破信号检测（传入盘口异动追踪器用于信号优化）
                        breakthrough_signals = historical_breakthrough_detector.detect_signals(display_stocks, current_sim_time, market_snapshot, order_flow_tracker)
                        if breakthrough_signals:
                            print(f"\n🚀 历史突破信号检测 (模拟时间点: {current_sim_time}) 🚀")

                            # 按主线强度分排序（优先），然后按综合评分排序
                            breakthrough_signals.sort(key=lambda x: (x.get('主线强度分', 0), x['综合评分']), reverse=True)

                            # 【【【性能优化：预先构建涨停股票的概念和行业映射，避免重复查询】】】
                            # 复用之前构建的映射，如果不存在则重新构建
                            if 'limit_up_concept_map' not in locals() or 'limit_up_industry_map' not in locals():
                                limit_up_concept_map = {}  # concept -> count
                                limit_up_industry_map = {}  # industry -> count

                                for limit_up_stock in current_limit_up_stocks:
                                    limit_up_name = limit_up_stock.get('名称', '')
                                    limit_up_code = limit_up_stock.get('代码', '')
                                    limit_up_industry = limit_up_stock.get('所属行业', '')

                                    # 统计行业涨停数量
                                    if limit_up_industry:
                                        limit_up_industry_map[limit_up_industry] = limit_up_industry_map.get(limit_up_industry, 0) + 1

                                    # 获取概念信息并统计
                                    if limit_up_name or limit_up_code:
                                        limit_up_sectors = get_stock_sectors(limit_up_name, limit_up_code)
                                        if limit_up_sectors is None:
                                            limit_up_sectors = {'concepts': [], 'industries': []}
                                        for concept in limit_up_sectors.get('concepts', []):
                                            limit_up_concept_map[concept] = limit_up_concept_map.get(concept, 0) + 1

                            # 格式化表格数据
                            signal_table_data = []
                            for signal in breakthrough_signals:
                                stock_name = signal['股票名称']
                                stock_code = signal.get('代码', '')

                                # 【【【新增：计算概念涨停数量和行业涨停数量 - 性能优化版】】】
                                # 获取股票的概念和行业信息
                                sectors_info = get_stock_sectors(stock_name, stock_code)
                                if sectors_info is None:
                                    sectors_info = {'concepts': [], 'industries': []}
                                stock_concepts = sectors_info.get('concepts', [])
                                stock_industries = sectors_info.get('industries', [])

                                # 统计该股票所属概念的涨停数量（使用预构建的映射）
                                concept_count = max([limit_up_concept_map.get(concept, 0) for concept in stock_concepts] + [0])

                                # 统计该股票所属行业的涨停数量（使用预构建的映射）
                                industry_count = max([limit_up_industry_map.get(industry, 0) for industry in stock_industries] + [0])

                                row = [
                                    signal['信号类型'],
                                    signal['股票名称'],
                                    signal['当前排名'],
                                    format_amount(signal['主力净流入']),
                                    signal['突破周期'],
                                    f"{signal['综合评分']:.2f}",
                                    signal.get('评级', 'N/A'),
                                    str(signal.get('主线强度分', 0)),
                                    concept_count,  # 新增概念涨停数量
                                    industry_count,  # 新增行业涨停数量
                                    signal.get('评分理由', '无'),
                                    signal.get('信号时间', current_sim_time)
                                ]
                                signal_table_data.append(row)

                            # 打印信号表格
                            signal_headers = ['信号类型', '股票名称', '当前排名', '主力净流入', '突破周期', '综合评分', '评级', '主线分', '概念涨停数', '行业涨停数', '评分理由', '信号时间']
                            print(tabulate(signal_table_data, headers=signal_headers, tablefmt='psql'))

                            # 写入专门的信号日志文件
                            if signal_log_path:
                                try:
                                    # 构建日志内容
                                    log_content = f"🚀 时间点: {current_sim_time} 🚀\n"
                                    log_content += f"检测到 {len(breakthrough_signals)} 个历史突破信号\n"
                                    log_content += "-" * 80 + "\n"

                                    # 写入表格格式的信号数据
                                    table_str = tabulate(signal_table_data, headers=signal_headers, tablefmt='grid')
                                    log_content += table_str + "\n\n"

                                    # 写入详细信息
                                    for signal in breakthrough_signals:
                                        signal_time = signal.get('信号时间', current_sim_time)
                                        # 修复：评分理由已经是字符串，不需要再用join处理
                                        reason_text = signal.get('评分理由', '无')
                                        log_content += f"• {signal['信号类型']}: {signal['股票名称']} " \
                                                      f"(时间{signal_time}, 排名{signal['当前排名']}, {format_amount(signal['主力净流入'])}, " \
                                                      f"{signal['突破周期']}, 评分{signal['综合评分']:.2f}, " \
                                                      f"评级{signal.get('评级', 'N/A')}, 主线分{signal.get('主线强度分', 0)}, " \
                                                      f"理由: {reason_text})\n"
                                    log_content += "\n" + "=" * 80 + "\n\n"

                                    # 同时写入总体日志和按小时分割的日志
                                    write_to_log_files(log_content, signal_log_path, hourly_signal_log)
                                except Exception as e:
                                    print(f"写入信号日志失败: {e}")

                        # 【【【盘口突袭信号验证和评级模块 - 第二步：验证，第三步：评级】】】
                        if intensive_order_flow_candidates:
                            print(f"\n⚡ 盘口突袭信号检测 (模拟时间点: {current_sim_time}) ⚡")

                            validated_assault_signals = []

                            for candidate in intensive_order_flow_candidates:
                                stock_name = candidate['stock_name']
                                signal_count = candidate['signal_count']

                                # 第二步：验证 - 检查是否在个股数据中且符合条件
                                stock_data = None
                                for _, row in display_stocks.iterrows():
                                    if row['名称'] == stock_name:
                                        stock_data = row
                                        break

                                if stock_data is None:
                                    continue  # 未在个股资金流前50中，跳过

                                # 验证条件
                                current_rank = stock_data.name + 1  # pandas index + 1
                                net_inflow = stock_data.get('今日主力净流入-净额', 0)
                                change_pct = stock_data.get('今日涨跌幅', 0)

                                # 验证标准：排名<500，净流入>0，涨跌幅>0%
                                if current_rank >= 500 or net_inflow <= 0 or change_pct <= 0:
                                    continue  # 不符合验证条件，跳过

                                # 第三步：评级 - 计算综合评分
                                # 盘口强度分 (最高10分)
                                order_flow_score = min(10, (signal_count - min_signal_count + 1) * 2.5)

                                # 构建信号字典用于主线强度评分
                                signal_dict = {
                                    '股票名称': stock_name,
                                    '代码': stock_data.get('代码', ''),
                                    '当前排名': current_rank,
                                    '主力净流入': net_inflow
                                }

                                # 主线强度评分
                                mainline_score, rating, reasons = mainline_scorer.score_signal(signal_dict, market_snapshot)

                                # 个股强度分 (最高10分)
                                individual_score = max(0, (500 - current_rank) / 500) * 10

                                # 综合评分 (盘口强度50%，主线强度30%，个股强度20%)
                                comprehensive_score = (order_flow_score * 0.5) + (mainline_score * 0.3) + (individual_score * 0.2)

                                # 根据综合评分确定最终评级
                                if comprehensive_score >= 8.0:
                                    final_rating = "★★★ [优先]"
                                elif comprehensive_score >= 5.0:
                                    final_rating = "★★☆ [关注]"
                                else:
                                    final_rating = "★☆☆ [观察]"

                                # 构建评分理由
                                reason_parts = [f"密集大单买入({signal_count}次)"]
                                if mainline_score > 0:
                                    reason_parts.append(f"命中主线({mainline_score}分)")
                                if reasons:
                                    # reasons 是一个列表，需要将其转换为字符串
                                    if isinstance(reasons, list):
                                        reason_parts.extend(reasons)
                                    else:
                                        reason_parts.append(str(reasons))

                                assault_signal = {
                                    '信号类型': '盘口突袭',
                                    '股票名称': stock_name,
                                    '当前排名': current_rank,
                                    '主力净流入': net_inflow,
                                    '盘口异动次数': signal_count,
                                    '盘口强度分': order_flow_score,
                                    '主线强度分': mainline_score,
                                    '个股强度分': individual_score,
                                    '综合评分': comprehensive_score,
                                    '评级': final_rating,
                                    '评分理由': ' | '.join(reason_parts),
                                    '信号时间': current_sim_time
                                }

                                validated_assault_signals.append(assault_signal)

                            if validated_assault_signals:
                                # 按综合评分排序
                                validated_assault_signals.sort(key=lambda x: x['综合评分'], reverse=True)

                                # 格式化表格数据
                                assault_table_data = []
                                for signal in validated_assault_signals:
                                    row = [
                                        signal['信号类型'],
                                        signal['股票名称'],
                                        signal['当前排名'],
                                        format_amount(signal['主力净流入']),
                                        f"{signal['盘口异动次数']}次",
                                        f"{signal['综合评分']:.2f}",
                                        signal['评级'],
                                        str(signal['主线强度分']),
                                        signal['评分理由'],
                                        signal['信号时间']
                                    ]
                                    assault_table_data.append(row)

                                # 打印盘口突袭信号表格
                                assault_headers = ['信号类型', '股票名称', '当前排名', '主力净流入', '异动次数', '综合评分', '评级', '主线分', '评分理由', '信号时间']
                                print(tabulate(assault_table_data, headers=assault_headers, tablefmt='psql'))

                                # 写入信号日志文件
                                if signal_log_path:
                                    try:
                                        # 构建日志内容
                                        log_content = f"⚡ 盘口突袭信号 - 时间点: {current_sim_time} ⚡\n"
                                        log_content += f"检测到 {len(validated_assault_signals)} 个盘口突袭信号\n"
                                        log_content += "-" * 80 + "\n"

                                        # 写入表格格式的信号数据
                                        table_str = tabulate(assault_table_data, headers=assault_headers, tablefmt='grid')
                                        log_content += table_str + "\n\n"

                                        # 写入详细信息
                                        for signal in validated_assault_signals:
                                            log_content += f"• {signal['信号类型']}: {signal['股票名称']} " \
                                                          f"(时间{signal['信号时间']}, 排名{signal['当前排名']}, {format_amount(signal['主力净流入'])}, " \
                                                          f"{signal['盘口异动次数']}次异动, 评分{signal['综合评分']:.2f}, " \
                                                          f"评级{signal['评级']}, 主线分{signal['主线强度分']}, " \
                                                          f"理由: {signal['评分理由']})\n"
                                        log_content += "\n" + "=" * 80 + "\n\n"

                                        # 同时写入总体日志和按小时分割的日志
                                        write_to_log_files(log_content, signal_log_path, hourly_signal_log)
                                    except Exception as e:
                                        print(f"写入盘口突袭信号日志失败: {e}")
                            else:
                                print("  经验证，无符合条件的盘口突袭信号")
                        # 【【【盘口突袭信号验证和评级模块结束】】】

            except Exception as e:
                print(f"信号检测失败: {e}")

        # 【V11.0 新增】建立并打印"点火监控池"
        if lurking_sectors_info:
            print("\n" + "*"*20 + " 🔥 点火监控池 🔥 " + "*"*20)
            print("以下潜伏板块及其核心个股已加入高级别监控，等待点火信号！")
            for sector, info in lurking_sectors_info.items():
                print(f"  - 潜伏板块: 【{sector}】")
                print(f"    - 核心标的: {', '.join(info['stocks'])}")
            print("*"*55)

        # ---------------------------------------------------------------------
        # ✅✅✅ 终极升级：在此处调用大师级决策模块 ✅✅✅
        # 确保在每个盘中时间点，所有基础数据都分析完毕后，立即进行梯队决策
        # ---------------------------------------------------------------------
        if 'all_sectors_df_sorted' in locals() and stock_flow_data is not None:
            tier1_stocks, tier2_stocks, tier3_stocks, core_pool_report = generate_tiered_watchlist_and_report(all_sectors_df_sorted, stock_flow_data, current_limit_up_stocks)

            # 写入核心股票池信号文件
            if core_pool_log_path and core_pool_report:
                try:
                    # 构建日志内容
                    log_content = f"🎯 时间点: {current_sim_time} 🎯\n"
                    log_content += core_pool_report + "\n\n"
                    log_content += "-" * 80 + "\n\n"

                    # 同时写入总体日志和按小时分割的日志
                    write_to_log_files(log_content, core_pool_log_path, hourly_core_pool_log)
                except Exception as e:
                    print(f"⚠️ 写入核心股票池信号文件失败: {e}")
        else:
            print("\n⚠️ 缺少板块或个股数据，无法生成核心股票池三梯队分析报告。")
        # ---------------------------------------------------------------------

        # ---------------------------------------------------------------------
        # 🎯🎯🎯 新增：战情总结模块 - 模拟顶级游资视角的盘面解读 🎯🎯🎯
        # 在所有分析模块完成后，进行联动校验和核心特征识别
        # ---------------------------------------------------------------------
        if 'all_sectors_df_sorted' in locals() and stock_flow_data is not None:
            # 分离概念和行业数据
            concepts_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '概念'] if 'all_sectors_df_sorted' in locals() else pd.DataFrame()
            industries_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '行业'] if 'all_sectors_df_sorted' in locals() else pd.DataFrame()

            generate_marshal_summary_report(market_snapshot, concepts_df, industries_df, stock_flow_data, current_limit_up_stocks, current_sim_time)
        else:
            print("\n⚠️ 缺少板块或个股数据，无法生成战情总结报告。")
        # ---------------------------------------------------------------------

        # 【【【核心修复：只用板块数据更新板块快照】】】
        previous_data_snapshot = all_sectors_df.set_index('名称').to_dict('index')

    print("\n" + "=" * 70)
    print(f"日期 {date_str} 的资金断层与加速度分析全部完成。")
    print("=" * 70)


def generate_marshal_summary_report(market_snapshot, concepts_df, industries_df, stock_flow_data, limit_up_stocks, current_sim_time):
    """
    【战情总结】模拟顶级游资视角的盘面解读报告

    通过联动校验不同模块的数据，自动识别"绝对主线"、"奇袭支线"、"最强补涨"三大核心盘面特征

    参数:
    - market_snapshot (dict): 包含当前时间点所有关键分析结果的字典
    - concepts_df (DataFrame): 排序后的概念板块数据
    - industries_df (DataFrame): 排序后的行业板块数据
    - stock_flow_data (DataFrame): 排序后的个股资金流数据
    - limit_up_stocks (list of dicts): 当前的涨停股池数据
    - current_sim_time (datetime.time): 当前模拟时间点

    返回值: 无，函数直接通过 print 输出战情总结报告
    """

    print("\n" + "⚔️" * 50)
    print("⚔️ 【战情总结】顶级游资视角的盘面解读 ⚔️")
    print("⚔️" * 50)

    # 第一步：识别"绝对主线"（主战场确认）
    _identify_absolute_mainline(market_snapshot, concepts_df, industries_df, stock_flow_data)

    # 第二步：识别"奇袭支线"（高低切换识别）
    _identify_surprise_sideline(limit_up_stocks, industries_df)

    # 第三步：识别"最强补涨"（主战场内部排序）
    _identify_strongest_follow_up(market_snapshot, stock_flow_data, limit_up_stocks, industries_df)

    print("⚔️" * 50)


def _identify_absolute_mainline(market_snapshot, concepts_df, industries_df, stock_flow_data):
    """第一步：识别"绝对主线"（主战场确认）"""
    try:
        # 获取个股资金断层龙头
        stock_gap_leader = market_snapshot.get('stock_gap_leader', '')
        if not stock_gap_leader:
            if DEBUG_MODE:
                print("\n--- ⚠️ 绝对主线识别失败 ⚠️ ---")
                print("【识别失败原因】：未发现个股资金断层龙头，无法进行主线确认。")
            return

        # 获取概念板块龙头分Top 1
        if concepts_df is None or concepts_df.empty:
            if DEBUG_MODE:
                print("\n--- ⚠️ 绝对主线识别失败 ⚠️ ---")
                print("【识别失败原因】：概念板块数据缺失，无法进行主线确认。")
            return

        top_concept_leader = concepts_df.iloc[0]['名称']

        # 查询股票所属的概念板块
        stock_sectors_info = get_stock_sectors(stock_gap_leader)
        if stock_sectors_info is None:
            stock_sectors_info = {'concepts': [], 'industries': []}
        stock_concepts = stock_sectors_info.get('concepts', [])
        stock_industries = stock_sectors_info.get('industries', [])

        # 联动校验：检查top_concept_leader是否在stock_gap_leader的所属概念列表中
        if top_concept_leader in stock_concepts:
            # 获取相关行业信息
            related_industry = "未知行业"
            if stock_flow_data is not None and not stock_flow_data.empty:
                # 查找股票在个股资金流数据中的行业信息
                stock_mask = stock_flow_data['名称'] == stock_gap_leader
                matching_stock = stock_flow_data.loc[stock_mask]
                if not matching_stock.empty and '所属行业' in matching_stock.columns:
                    related_industry = matching_stock.iloc[0].get('所属行业', '未知行业')
                elif stock_industries:
                    # 使用从板块映射获取的行业信息
                    related_industry = stock_industries[0]

            print("\n--- ⚔️ 盘面核心：绝对主线已现 ⚔️ ---")
            print(f"【绝对主线识别】：{top_concept_leader}线 ({top_concept_leader}/{related_industry})")
            print(f"【核心逻辑】：板块龙头分第一({top_concept_leader})，与全市场资金总龙头【{stock_gap_leader}】形成\"将帅合一\"的最强格局。")
        else:
            if DEBUG_MODE:
                print("\n--- ⚠️ 绝对主线识别失败 ⚠️ ---")
                print(f"【识别失败原因】：概念龙头【{top_concept_leader}】与个股龙头【{stock_gap_leader}】未形成联动，缺乏\"将帅合一\"格局。")
                if stock_concepts:
                    print(f"【个股所属概念】：{', '.join(stock_concepts[:3])}")

    except Exception as e:
        print(f"\n--- ⚠️ 绝对主线识别异常 ⚠️ ---")
        print(f"【异常信息】：{e}")


def _identify_surprise_sideline(limit_up_stocks, industries_df):
    """第二步：识别"奇袭支线"（高低切换识别）"""
    try:
        if not limit_up_stocks:
            if DEBUG_MODE:
                print("\n--- ⚠️ 奇袭支线识别失败 ⚠️ ---")
                print("【识别失败原因】：无涨停股池数据，无法进行奇袭支线分析。")
            return

        # 筛选早盘涨停股（首次封板时间在09:40:00之前）
        early_limit_up_stocks = []
        for stock in limit_up_stocks:
            first_seal_time = stock.get('首次封板时间', '')
            if first_seal_time:
                # 处理不同的时间格式
                time_str = str(first_seal_time).strip()

                # 如果是HHMMSS格式（如092500），转换为可比较的整数
                if len(time_str) == 6 and time_str.isdigit():  # 092500格式
                    time_int = int(time_str)
                    # 09:40:00 对应 94000
                    if time_int <= 94000:
                        early_limit_up_stocks.append(stock)
                elif len(time_str) == 5 and time_str.isdigit():  # 92500格式
                    time_int = int(f"0{time_str}")
                    if time_int <= 94000:
                        early_limit_up_stocks.append(stock)
                else:
                    # 如果是HH:MM:SS格式，转换后比较
                    if time_str <= "09:40:00":
                        early_limit_up_stocks.append(stock)

        if not early_limit_up_stocks:
            if DEBUG_MODE:
                print("\n--- ⚠️ 奇袭支线识别失败 ⚠️ ---")
                print("【识别失败原因】：无早盘涨停股（09:40前封板），无法识别奇袭支线。")
            return

        # 对早盘涨停股的所属行业进行归类和计数
        industry_count = {}
        industry_stocks = {}  # 记录每个行业的股票

        for stock in early_limit_up_stocks:
            industry = stock.get('所属行业', '未知行业')
            if industry and industry != '未知行业':
                if industry not in industry_count:
                    industry_count[industry] = 0
                    industry_stocks[industry] = []
                industry_count[industry] += 1
                industry_stocks[industry].append(stock)

        # 寻找非主线行业（在industries_df中排名>3），且早盘涨停家数>=2
        surprise_candidates = []

        if industries_df is not None and not industries_df.empty:
            # 获取行业排名前3的主线行业
            top3_industries = set(industries_df.head(3)['名称'].tolist())

            for industry, count in industry_count.items():
                if count >= 2 and industry not in top3_industries:
                    # 检查是否包含20CM涨停（股票代码以'300'或'688'开头）
                    has_20cm = False
                    cm20_stocks = []

                    for stock in industry_stocks[industry]:
                        stock_code = str(stock.get('代码', ''))
                        if stock_code and (stock_code.startswith('300') or stock_code.startswith('688')):
                            has_20cm = True
                            cm20_stocks.append(stock['名称'])

                    if has_20cm:
                        surprise_candidates.append({
                            'industry': industry,
                            'count': count,
                            'stocks': [s['名称'] for s in industry_stocks[industry]],
                            'cm20_stocks': cm20_stocks
                        })

        # 输出奇袭支线识别结果
        if surprise_candidates:
            for candidate in surprise_candidates:
                # 生成泛行业名称
                generic_industry = _get_generic_industry_name(candidate['industry'])

                print("\n--- 🎯 盘面机会：奇袭支线突现 🎯 ---")
                print(f"【奇袭支线识别】：{generic_industry}线")

                # 构建股票列表显示
                stock_list = candidate['stocks'][:4]  # 最多显示4只
                stock_display = '、'.join(stock_list)
                if len(candidate['stocks']) > 4:
                    stock_display += "等"

                cm20_leader = candidate['cm20_stocks'][0] if candidate['cm20_stocks'] else "未知"

                print(f"【核心逻辑】：开盘后迅速形成【{stock_display}】等多股联动的板块效应，且先锋【{cm20_leader}】为20CM稀缺形态，是高低切换资金攻击的明确信号。")
        else:
            if DEBUG_MODE:
                print("\n--- ⚠️ 奇袭支线识别失败 ⚠️ ---")
                print("【识别失败原因】：未发现符合条件的奇袭支线（需要非主线行业+早盘涨停>=2只+包含20CM龙头）。")

    except Exception as e:
        print(f"\n--- ⚠️ 奇袭支线识别异常 ⚠️ ---")
        print(f"【异常信息】：{e}")


def _identify_strongest_follow_up(market_snapshot, stock_flow_data, limit_up_stocks, industries_df):
    """第三步：识别"最强补涨"（主战场内部排序）"""
    try:
        # 依赖于第一步成功识别出stock_gap_leader
        stock_gap_leader = market_snapshot.get('stock_gap_leader', '')
        if not stock_gap_leader:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print("【识别失败原因】：未发现个股资金断层龙头，无法进行主战场内部排序。")
            return

        if stock_flow_data is None or stock_flow_data.empty:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print("【识别失败原因】：个股资金流数据缺失，无法进行补涨分析。")
            return

        # 获取stock_gap_leader的所属行业
        leader_industry = None
        leader_mask = stock_flow_data['名称'] == stock_gap_leader
        leader_data = stock_flow_data.loc[leader_mask]

        if not leader_data.empty and '所属行业' in leader_data.columns:
            leader_industry = leader_data.iloc[0].get('所属行业', '')

        if not leader_industry:
            # 尝试从板块映射获取行业信息
            leader_sectors_info = get_stock_sectors(stock_gap_leader)
            if leader_sectors_info is None:
                leader_sectors_info = {'concepts': [], 'industries': []}
            leader_industries = leader_sectors_info.get('industries', [])
            if leader_industries:
                leader_industry = leader_industries[0]

        if not leader_industry:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print(f"【识别失败原因】：无法确定龙头股【{stock_gap_leader}】的所属行业。")
            return

        # 在stock_flow_data中筛选出所有隶属于该行业的股票
        if '所属行业' in stock_flow_data.columns:
            same_industry_stocks = stock_flow_data[stock_flow_data['所属行业'] == leader_industry].copy()
        else:
            # 如果没有行业列，通过板块映射查找同行业股票
            same_industry_stocks = []
            for idx, row in stock_flow_data.iterrows():
                stock_name = row['名称']
                stock_sectors_info = get_stock_sectors(stock_name)
                if stock_sectors_info is None:
                    stock_sectors_info = {'concepts': [], 'industries': []}
                stock_industries = stock_sectors_info.get('industries', [])
                if leader_industry in stock_industries:
                    same_industry_stocks.append(row)

            if same_industry_stocks:
                same_industry_stocks = pd.DataFrame(same_industry_stocks)
            else:
                same_industry_stocks = pd.DataFrame()

        if same_industry_stocks.empty or len(same_industry_stocks) < 2:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print(f"【识别失败原因】：行业【{leader_industry}】内股票数量不足，无法进行补涨排序。")
            return

        # 按资金流入排序，找到仅次于stock_gap_leader的第二名
        same_industry_stocks = same_industry_stocks.sort_values('今日主力净流入-净额', ascending=False)

        # 找到龙头在同行业中的位置
        leader_rank_in_industry = None
        strongest_follower = None

        for idx, (_, row) in enumerate(same_industry_stocks.iterrows()):
            if row['名称'] == stock_gap_leader:
                leader_rank_in_industry = idx + 1
                # 找第二名（如果龙头是第一名）
                if idx == 0 and len(same_industry_stocks) > 1:
                    strongest_follower = same_industry_stocks.iloc[1]['名称']
                break

        if not strongest_follower:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print(f"【识别失败原因】：在行业【{leader_industry}】内未找到合适的补涨标的。")
            return

        # 从limit_up_stocks中查找strongest_follower的涨停信息
        follower_limit_info = None
        if limit_up_stocks:
            for stock in limit_up_stocks:
                if stock.get('名称') == strongest_follower:
                    follower_limit_info = stock
                    break

        # 检查涨停形态是否强硬
        is_strong_limit_up = False
        limit_up_desc = "未涨停"

        if follower_limit_info:
            first_seal_time = follower_limit_info.get('首次封板时间', '')
            if first_seal_time:
                # 处理不同的时间格式
                time_str = str(first_seal_time).strip()

                # 如果是HHMMSS格式（如092500），转换为可比较的整数
                if len(time_str) == 6 and time_str.isdigit():  # 092500格式
                    time_int = int(time_str)
                    # 10:00:00 对应 100000
                    if time_int <= 100000:
                        is_strong_limit_up = True
                        # 转换为显示格式
                        formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
                        limit_up_desc = f"早盘封板({formatted_time})"
                    else:
                        formatted_time = f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
                        limit_up_desc = f"封板时间({formatted_time})"
                elif len(time_str) == 5 and time_str.isdigit():  # 92500格式
                    time_int = int(f"0{time_str}")
                    if time_int <= 100000:
                        is_strong_limit_up = True
                        formatted_time = f"0{time_str[:1]}:{time_str[1:3]}:{time_str[3:5]}"
                        limit_up_desc = f"早盘封板({formatted_time})"
                    else:
                        formatted_time = f"0{time_str[:1]}:{time_str[1:3]}:{time_str[3:5]}"
                        limit_up_desc = f"封板时间({formatted_time})"
                else:
                    # 如果是HH:MM:SS格式，直接比较
                    if time_str <= "10:00:00":
                        is_strong_limit_up = True
                        limit_up_desc = f"早盘封板({time_str})"
                    else:
                        limit_up_desc = f"封板时间({time_str})"

        # 输出最强补涨识别结果
        if is_strong_limit_up or follower_limit_info:  # 只要涨停就认为有效
            print("\n--- 🚀 核心机会：最强补涨已定 🚀 ---")
            print(f"【最强补涨识别】：【{strongest_follower}】")
            print(f"【核心逻辑】：隶属绝对主线({leader_industry})，板块内资金强度仅次于总龙头【{stock_gap_leader}】，且涨停形态强硬({limit_up_desc})，是主线最强的活口和补涨核心。")
        else:
            if DEBUG_MODE:
                print("\n--- ⚠️ 最强补涨识别失败 ⚠️ ---")
                print(f"【识别失败原因】：候选股【{strongest_follower}】未涨停或涨停形态不够强硬，不符合补涨标准。")

    except Exception as e:
        print(f"\n--- ⚠️ 最强补涨识别异常 ⚠️ ---")
        print(f"【异常信息】：{e}")


def _get_generic_industry_name(industry):
    """将具体行业名称转换为泛行业名称"""
    industry_mapping = {
        # 医药相关
        '医疗器械': '医药',
        '化学制药': '医药',
        '中药': '医药',
        '生物制药': '医药',
        '医疗服务': '医药',
        '医药商业': '医药',

        # 科技相关
        '软件开发': '科技',
        '半导体': '科技',
        '电子元件': '科技',
        '通信设备': '科技',
        '计算机设备': '科技',

        # 新能源相关
        '电池': '新能源',
        '光伏设备': '新能源',
        '风电设备': '新能源',
        '储能技术': '新能源',

        # 制造业相关
        '专用设备': '制造',
        '通用设备': '制造',
        '汽车零部件': '制造',
        '机械设备': '制造'
    }

    return industry_mapping.get(industry, industry)


def test_stock_flow_file_format_recognition():
    """测试个股资金流文件格式识别的完整性"""
    print("=== 个股资金流文件格式识别测试 ===")

    # 测试所有原有支持的文件类型
    test_files = [
        'fund_flow_rank_20250725_093047.csv',     # rank_format
        'fund_flow_tpdog.csv',                    # tpdog_format
        'ths_fund_flow.csv',                      # ths_format
        'fund_flow_akshare.csv',                  # akshare_format
        'individual_fund_flow_20250725.csv',      # individual_format
        '股票资金流_zssz_20250728_093050.csv',    # exchange_format
        '股票资金流_zssh_20250728_093138.csv',    # exchange_format
        'stock_fund_flow_test.csv',               # stock_flow_format
        'unknown_format.csv'                      # standard_format (默认)
    ]
    
    for test_file in test_files:
        file_format = get_stock_flow_file_format(test_file)
        filter_strategy = get_stock_filter_strategy(file_format)
        print(f"文件: {test_file}")
        print(f"  格式: {file_format}")
        print(f"  过滤策略: {filter_strategy}")
        print()
    
    print("=== 格式识别覆盖率检查 ===")
    original_patterns = [
        'fund_flow_rank_',      # ✓ 已覆盖
        'fund_flow_tpdog.csv',  # ✓ 已覆盖
        'ths_fund_flow.csv',    # ✓ 已覆盖
        'fund_flow_akshare.csv',# ✓ 已覆盖
        'individual_fund_flow_',# ✓ 已覆盖
        '股票资金流_zssz_',      # ✓ 已覆盖
        '股票资金流_zssh_',      # ✓ 已覆盖
        'stock_fund_flow_'      # ✓ 已覆盖
    ]
    
    print("原有文件模式覆盖情况:")
    for pattern in original_patterns:
        print(f"  {pattern}: ✓ 已覆盖")


# 在主函数中可以调用此测试函数
# test_stock_flow_file_format_recognition()


def test_file_classification():
    """测试文件分类功能，验证是否能正确识别用户提到的所有文件类型"""
    test_files = [
        # 大单买盘文件
        "09-30_ths_big_deal.csv",
        "09-30_movers_大笔买入.csv",

        # 板块异动文件
        "09-30_board_changes.csv",

        # 涨停股池文件
        "limit_up_pool_20250728_093026.csv",
        "09-30_zt_pool.csv",

        # 创月新高指标文件
        "09-30_indicator_创月新高.csv",

        # 个股资金流文件（新格式）
        "股票资金流_zssz_20250728_093050.csv",
        "股票资金流_zssh_20250728_093138.csv",
        "股票资金流_zssz_20250728_093140.csv",
        "09-34_fund_flow_tpdog.csv",

        # 概念/板块资金流文件
        "09-34_concept_fund_flow_tpdog.csv",
        "实时概念资金流_20250728_093453.csv",

        # 原有支持的文件
        "fund_flow_rank_20250725_093047.csv",
        "concept_fund_flow_akshare.csv",
        "sector_fund_flow_tpdog.csv"
    ]

    print("=== 文件分类测试 ===")
    for filename in test_files:
        file_type = classify_file_type(filename)
        timestamp = extract_timestamp_from_filename(filename)
        print(f"文件: {filename}")
        print(f"  类型: {file_type}")
        print(f"  时间戳: {timestamp}")
        print()

    print("=== 时间戳提取测试 ===")
    time_test_files = [
        "09-30_ths_big_deal.csv",  # 应该提取到 093000
        "limit_up_pool_20250728_093026.csv",  # 应该提取到 093026
        "股票资金流_zssz_20250728_093050.csv",  # 应该提取到 093050
        "实时概念资金流_20250728_093453.csv",  # 应该提取到 093453
        "09-34_fund_flow_tpdog.csv"  # 应该提取到 093400
    ]

    for filename in time_test_files:
        timestamp = extract_timestamp_from_filename(filename)
        print(f"{filename} -> {timestamp}")


def generate_tiered_watchlist_and_report(all_sectors_df_sorted, stock_flow_data, limit_up_stocks):
    """
    【终极筛选】生成三梯队核心股票池和决策报告

    模块核心思想: 源自顶级游资的"主线聚焦"策略：只在最强的战场，干最强的龙头。
    通过联动现有的所有分析结果（龙头评分、资金排名、涨停形态等），进行一次"终极筛选"，
    输出一个极度精简、分层级的核心股票池。

    参数:
    - all_sectors_df_sorted: 已按leadership_score排序的板块数据
    - stock_flow_data: 个股资金流数据
    - limit_up_stocks: 涨停股池数据

    返回:
    - tier1_stocks: 第一梯队股票列表
    - tier2_stocks: 第二梯队股票列表
    - tier3_stocks: 第三梯队股票列表
    - report: 完整的分析报告字符串
    """
    try:
        print("\n" + "🎯" * 30)
        print("🎯 开始计算联动分，确立主战场...")
        print("🎯" * 30)

        # 第一步：三维筛选法则确立主战场
        print("📊 确立主战场板块...")
        main_battlefields = set()  # 绝对主战场
        potential_battlefields = set()  # 潜在主战场

        if all_sectors_df_sorted is not None and not all_sectors_df_sorted.empty and limit_up_stocks:
            # 1. 计算当前市场最高连板数
            market_max_consecutive = 1
            if limit_up_stocks:
                consecutive_values = []
                for stock in limit_up_stocks:
                    consecutive_days = stock.get('连板数', 1)
                    try:
                        consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
                    except:
                        consecutive_days = 1
                    consecutive_values.append(consecutive_days)
                market_max_consecutive = max(consecutive_values) if consecutive_values else 1

            print(f"🔥 当前市场最高连板数: {market_max_consecutive}板")

            # 2. 获取资金排名前三的板块
            capital_sorted = all_sectors_df_sorted.sort_values(by='今日主力净流入-净额', ascending=False)
            top_3_capital_sectors = set(capital_sorted.head(3)['名称'].tolist())
            print(f"💰 资金前三板块: {', '.join(list(top_3_capital_sectors))}")

            # 3. 对每个板块进行三维评估
            print("🔍 开始三维筛选评估...")
            for _, row in all_sectors_df_sorted.iterrows():
                sector_name = row['名称']
                max_consecutive = row.get('max_consecutive', 0)
                limit_up_count = row.get('limit_up_count', 0)

                # 三个维度的布尔判断
                # 情绪核心：拥有市场最高连板梯队
                emotion_core = (max_consecutive >= market_max_consecutive and market_max_consecutive >= 2)

                # 资金认可：资金净流入前三名
                capital_approval = sector_name in top_3_capital_sectors

                # 梯队完整：健康的涨停梯队（高度≥2板 且 广度≥2家）
                cohort_integrity = (max_consecutive >= 2 and limit_up_count >= 2)

                # 统计满足的条件数量
                conditions_met = sum([emotion_core, capital_approval, cohort_integrity])

                # 分类逻辑
                if conditions_met == 3:
                    main_battlefields.add(sector_name)
                    print(f"  ⭐ 绝对主战场: {sector_name} (情绪✅ 资金✅ 梯队✅)")
                elif conditions_met == 2:
                    potential_battlefields.add(sector_name)
                    condition_status = f"情绪{'✅' if emotion_core else '❌'} 资金{'✅' if capital_approval else '❌'} 梯队{'✅' if cohort_integrity else '❌'}"
                    print(f"  🔸 潜在主战场: {sector_name} ({condition_status})")

            print(f"🏟️ 绝对主战场: {', '.join(list(main_battlefields)) if main_battlefields else '无'}")
            print(f"🎪 潜在主战场: {', '.join(list(potential_battlefields)) if potential_battlefields else '无'}")
        else:
            print("⚠️ 数据不足，无法进行三维筛选")

        # 第二步：创建板块评分映射字典（性能优化）
        sector_score_map = {}
        if all_sectors_df_sorted is not None and not all_sectors_df_sorted.empty:
            for _, row in all_sectors_df_sorted.iterrows():
                sector_name = row['名称']
                leadership_score = row.get('leadership_score', 0)
                sector_score_map[sector_name] = leadership_score

        # 第三步：计算个股联动分
        print("⚡ 开始计算个股联动分...")

        if stock_flow_data is None or stock_flow_data.empty:
            print("⚠️ 个股资金流数据为空，无法计算联动分")
            return [], [], [], "⚠️ 数据不足，无法生成核心股票池报告"

        # 获取Top 50个股进行分析
        top50_stocks = stock_flow_data.head(50).copy()

        # 创建涨停股票集合，便于快速查找
        limit_up_stock_names = set()
        if limit_up_stocks:
            for stock in limit_up_stocks:
                stock_name = stock.get('名称', '')
                if stock_name:
                    limit_up_stock_names.add(stock_name)

        # 为每只股票计算联动分
        synergy_scores = []

        for idx, row in top50_stocks.iterrows():
            stock_name = row['名称']
            stock_code = row.get('代码', '')
            net_inflow = row.get('今日主力净流入-净额', 0)
            change_pct = row.get('涨跌幅', 0)

            # 获取股票的板块信息
            sectors_info = get_stock_sectors(stock_name, stock_code)
            if sectors_info is None:
                sectors_info = {'concepts': [], 'industries': []}

            all_stock_sectors = sectors_info.get('concepts', []) + sectors_info.get('industries', [])

            # 计算联动分的三个组成部分

            # 1. 个股排名分 (满分4分): 资金排名越靠前，得分越高
            ranking_score = max(0, 4 * (51 - (idx + 1)) / 50)  # idx+1是排名，转换为得分

            # 2. 板块加成 (满分6分): 根据所属板块的leadership_score加权
            sector_bonus = 0
            max_sector_score = 0
            best_sector = ""

            for sector in all_stock_sectors:
                if sector in sector_score_map:
                    sector_score = sector_score_map[sector]
                    if sector_score > max_sector_score:
                        max_sector_score = sector_score
                        best_sector = sector

            # 根据板块类型给予不同的加成
            if best_sector in main_battlefields:
                sector_bonus = max_sector_score * 6  # 主战场：满权重
            elif best_sector in potential_battlefields:
                sector_bonus = max_sector_score * 4  # 潜在战场：降权重
            else:
                sector_bonus = max_sector_score * 2  # 其他板块：低权重

            # 3. 赚钱效应暴击 (满分5分): 涨停或大涨
            money_effect_score = 0
            if stock_name in limit_up_stock_names:
                money_effect_score = 5  # 涨停：满分
            elif change_pct > 9.5:
                money_effect_score = 4  # 接近涨停：高分
            elif change_pct > 7:
                money_effect_score = 3  # 大涨：中等分
            elif change_pct > 5:
                money_effect_score = 2  # 中涨：低分

            # 计算总联动分
            total_synergy_score = ranking_score + sector_bonus + money_effect_score

            synergy_scores.append({
                'stock_name': stock_name,
                'stock_code': stock_code,
                'ranking': idx + 1,
                'net_inflow': net_inflow,
                'change_pct': change_pct,
                'synergy_score': total_synergy_score,
                'ranking_score': ranking_score,
                'sector_bonus': sector_bonus,
                'money_effect_score': money_effect_score,
                'best_sector': best_sector,
                'all_sectors': all_stock_sectors,
                'is_limit_up': stock_name in limit_up_stock_names
            })

        # 第四步：智能分层归类
        print("🏆 开始智能分层归类...")

        # 筛选出联动分 > 8.0 的股票
        qualified_stocks = [stock for stock in synergy_scores if stock['synergy_score'] > 8.0]

        if not qualified_stocks:
            print("⚠️ 没有股票的联动分超过8.0，降低标准到6.0")
            qualified_stocks = [stock for stock in synergy_scores if stock['synergy_score'] > 6.0]

        # 调用辅助函数进行分层和生成报告
        tier1_stocks, tier2_stocks, tier3_stocks, report = _generate_tiered_report(
            qualified_stocks, main_battlefields, potential_battlefields
        )

        return tier1_stocks, tier2_stocks, tier3_stocks, report

    except Exception as e:
        print(f"❌ 生成核心股票池失败: {e}")
        import traceback
        traceback.print_exc()
        return [], [], [], f"❌ 生成核心股票池失败: {e}"


def _generate_tiered_report(qualified_stocks, main_battlefields, potential_battlefields):
    """
    【辅助函数】专门用于分层和生成报告文本，保持主函数逻辑清晰

    参数:
    - qualified_stocks: 符合条件的股票列表
    - main_battlefields: 主战场板块集合
    - potential_battlefields: 潜在战场板块集合

    返回:
    - tier1_stocks, tier2_stocks, tier3_stocks, report
    """
    try:
        tier1_stocks = []  # 龙头中的龙头
        tier2_stocks = []  # 板块内中军
        tier3_stocks = []  # 独立趋势龙头

        # 分层逻辑
        for stock in qualified_stocks:
            ranking = stock['ranking']
            is_limit_up = stock['is_limit_up']
            all_sectors = stock['all_sectors']

            # 判断是否属于任何战场
            belongs_to_main = any(sector in main_battlefields for sector in all_sectors)
            belongs_to_potential = any(sector in potential_battlefields for sector in all_sectors)
            belongs_to_any_battlefield = belongs_to_main or belongs_to_potential

            # 第一梯队 (龙头中的龙头): 必须同时满足：身处"主战场" + 自身已涨停 + 资金排名<=20
            if belongs_to_main and is_limit_up and ranking <= 20:
                tier1_stocks.append(stock)
            # 第三梯队 (独立趋势龙头): 不属于任何战场，但资金排名<=5
            elif not belongs_to_any_battlefield and ranking <= 5:
                tier3_stocks.append(stock)
            # 第二梯队 (板块内中军): 属于"主战场"或"潜在战场"的其他所有满足条件的优质股
            elif belongs_to_any_battlefield:
                tier2_stocks.append(stock)

        # 按联动分排序各梯队
        tier1_stocks.sort(key=lambda x: x['synergy_score'], reverse=True)
        tier2_stocks.sort(key=lambda x: x['synergy_score'], reverse=True)
        tier3_stocks.sort(key=lambda x: x['synergy_score'], reverse=True)

        # 生成决策报告
        report = _generate_decision_report(tier1_stocks, tier2_stocks, tier3_stocks, main_battlefields, potential_battlefields)

        return tier1_stocks, tier2_stocks, tier3_stocks, report

    except Exception as e:
        print(f"❌ 分层报告生成失败: {e}")
        return [], [], [], f"❌ 分层报告生成失败: {e}"


def _generate_decision_report(tier1_stocks, tier2_stocks, tier3_stocks, main_battlefields, potential_battlefields):
    """
    生成结构清晰的《核心股票池三梯队分析》报告
    """
    try:
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🎯 《核心股票池三梯队分析》- 顶级游资主线聚焦策略")
        report_lines.append("=" * 80)
        report_lines.append("")

        # 战场概况
        report_lines.append("📊 【战场概况】")
        report_lines.append(f"🏟️ 主战场 (Top 5): {', '.join(list(main_battlefields)[:5])}")
        report_lines.append(f"🎪 潜在战场 (Top 6-15): {', '.join(list(potential_battlefields)[:5])}")
        report_lines.append("")

        # 第一梯队
        report_lines.append("🥇 【第一梯队 - 龙头中的龙头】")
        report_lines.append("筛选标准: 主战场 + 已涨停 + 资金排名≤20")
        if tier1_stocks:
            table_data = []
            for i, stock in enumerate(tier1_stocks[:5]):  # 最多显示5只
                table_data.append([
                    i + 1,
                    stock['stock_name'],
                    f"{stock['synergy_score']:.2f}",
                    stock['ranking'],
                    stock['best_sector'],
                    "是" if stock['is_limit_up'] else "否",
                    format_amount(stock['net_inflow']),
                    "主战场龙头，资金+情绪双优"
                ])

            headers = ['优先级', '股票名称', '联动分', '资金排名', '所属概念', '是否涨停', '净流入', '决策解读']
            table_str = tabulate(table_data, headers=headers, tablefmt='grid')
            report_lines.append(table_str)
        else:
            report_lines.append("暂无符合条件的第一梯队股票")
        report_lines.append("")

        # 第二梯队
        report_lines.append("🥈 【第二梯队 - 板块内中军】")
        report_lines.append("筛选标准: 主战场或潜在战场内的优质股")
        if tier2_stocks:
            table_data = []
            for i, stock in enumerate(tier2_stocks[:8]):  # 最多显示8只
                battlefield_type = "主战场" if any(sector in main_battlefields for sector in stock['all_sectors']) else "潜在战场"
                table_data.append([
                    i + 1,
                    stock['stock_name'],
                    f"{stock['synergy_score']:.2f}",
                    stock['ranking'],
                    stock['best_sector'],
                    "是" if stock['is_limit_up'] else "否",
                    format_amount(stock['net_inflow']),
                    f"{battlefield_type}补涨机会"
                ])

            headers = ['优先级', '股票名称', '联动分', '资金排名', '所属概念', '是否涨停', '净流入', '决策解读']
            table_str = tabulate(table_data, headers=headers, tablefmt='grid')
            report_lines.append(table_str)
        else:
            report_lines.append("暂无符合条件的第二梯队股票")
        report_lines.append("")

        # 第三梯队
        report_lines.append("🥉 【第三梯队 - 独立趋势龙头】")
        report_lines.append("筛选标准: 不属于任何战场但资金排名≤5")
        if tier3_stocks:
            table_data = []
            for i, stock in enumerate(tier3_stocks[:3]):  # 最多显示3只
                table_data.append([
                    i + 1,
                    stock['stock_name'],
                    f"{stock['synergy_score']:.2f}",
                    stock['ranking'],
                    stock['best_sector'] if stock['best_sector'] else "独立个股",
                    "是" if stock['is_limit_up'] else "否",
                    format_amount(stock['net_inflow']),
                    "独立强势，关注轮动机会"
                ])

            headers = ['优先级', '股票名称', '联动分', '资金排名', '所属概念', '是否涨停', '净流入', '决策解读']
            table_str = tabulate(table_data, headers=headers, tablefmt='grid')
            report_lines.append(table_str)
        else:
            report_lines.append("暂无符合条件的第三梯队股票")
        report_lines.append("")

        # 总结
        total_stocks = len(tier1_stocks) + len(tier2_stocks) + len(tier3_stocks)
        report_lines.append("📈 【决策总结】")
        report_lines.append(f"本次筛选出核心股票池共 {total_stocks} 只，分布为：")
        report_lines.append(f"  第一梯队: {len(tier1_stocks)} 只 (重点关注)")
        report_lines.append(f"  第二梯队: {len(tier2_stocks)} 只 (补涨机会)")
        report_lines.append(f"  第三梯队: {len(tier3_stocks)} 只 (轮动机会)")
        report_lines.append("")
        report_lines.append("🎯 操作建议: 优先配置第一梯队，适度关注第二梯队，第三梯队作为轮动备选。")
        report_lines.append("=" * 80)

        # 打印报告到控制台
        full_report = "\n".join(report_lines)
        print(full_report)

        return full_report

    except Exception as e:
        print(f"❌ 决策报告生成失败: {e}")
        return f"❌ 决策报告生成失败: {e}"


if __name__ == "__main__":
    # 运行测试（可选）
    # test_file_classification()

    # 运行主分析
    run_gap_analysis_backtest(BACKTEST_DATE)